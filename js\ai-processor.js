/**
 * AI Processor for converting content using Gemini API
 * Handles chunking, batching, and intelligent content processing
 */

class AIProcessor {
    constructor() {
        this.isProcessing = false;
        this.isPaused = false;
        this.currentBatch = 0;
        this.totalBatches = 0;
        this.processedItems = [];
        this.failedItems = [];
        this.processingQueue = [];
        this.results = new Map();
        this.startTime = null;
        this.settings = CONFIG.DEFAULT_SETTINGS;
    }
    
    /**
     * Start processing data with AI
     */
    async startProcessing(data, customPrompt = '', settings = {}) {
        try {
            if (this.isProcessing) {
                throw new Error('المعالجة قيد التشغيل بالفعل');
            }
            
            this.isProcessing = true;
            this.isPaused = false;
            this.startTime = Date.now();
            this.settings = { ...this.settings, ...settings };
            
            // Initialize processing state
            this.initializeProcessing(data);
            
            // Prepare processing queue
            this.prepareProcessingQueue(data, customPrompt);
            
            // Start processing
            await this.processQueue();
            
            // Return results
            return this.getResults();
            
        } catch (error) {
            this.handleProcessingError(error);
            throw error;
        } finally {
            this.isProcessing = false;
        }
    }
    
    /**
     * Initialize processing state
     */
    initializeProcessing(data) {
        this.processedItems = [];
        this.failedItems = [];
        this.results.clear();
        this.currentBatch = 0;
        this.totalBatches = Math.ceil(data.length / CONFIG.PROCESSING.BATCH_SIZE);
        
        // Update UI
        this.showProgressSection();
        this.updateProgress(0, 'جاري التحضير...', 'يتم تحضير البيانات للمعالجة');
    }
    
    /**
     * Prepare processing queue with intelligent chunking
     */
    prepareProcessingQueue(data, customPrompt) {
        this.processingQueue = [];
        
        data.forEach((item, index) => {
            // Check if content needs chunking
            if (item.content.length > CONFIG.PROCESSING.MAX_CHUNK_SIZE) {
                const chunks = this.chunkContent(item.content);
                chunks.forEach((chunk, chunkIndex) => {
                    this.processingQueue.push({
                        id: `${item.id}_chunk_${chunkIndex}`,
                        originalId: item.id,
                        content: chunk,
                        isChunk: true,
                        chunkIndex,
                        totalChunks: chunks.length,
                        originalItem: item,
                        customPrompt,
                        batchIndex: Math.floor(this.processingQueue.length / CONFIG.PROCESSING.BATCH_SIZE)
                    });
                });
            } else {
                this.processingQueue.push({
                    id: item.id,
                    originalId: item.id,
                    content: item.content,
                    isChunk: false,
                    originalItem: item,
                    customPrompt,
                    batchIndex: Math.floor(this.processingQueue.length / CONFIG.PROCESSING.BATCH_SIZE)
                });
            }
        });
        
        // Update total batches based on queue
        this.totalBatches = Math.ceil(this.processingQueue.length / CONFIG.PROCESSING.BATCH_SIZE);
    }
    
    /**
     * Process the queue in batches
     */
    async processQueue() {
        const batches = this.createBatches(this.processingQueue);
        
        for (let i = 0; i < batches.length; i++) {
            if (!this.isProcessing || this.isPaused) {
                break;
            }
            
            this.currentBatch = i + 1;
            const batch = batches[i];
            
            // Update progress
            const progress = (i / batches.length) * 100;
            this.updateProgress(
                progress,
                `معالجة المجموعة ${this.currentBatch} من ${this.totalBatches}`,
                `جاري معالجة ${batch.length} عنصر...`
            );
            
            // Process batch
            await this.processBatch(batch);
            
            // Add delay between batches to respect rate limits
            if (i < batches.length - 1) {
                await this.delay(CONFIG.PROCESSING.DELAY_BETWEEN_BATCHES);
            }
        }
        
        // Combine chunked results
        await this.combineChunkedResults();
        
        // Update final progress
        this.updateProgress(100, 'اكتملت المعالجة', 'تم إنهاء جميع العمليات بنجاح');
    }
    
    /**
     * Create batches from processing queue
     */
    createBatches(queue) {
        const batches = [];
        for (let i = 0; i < queue.length; i += CONFIG.PROCESSING.BATCH_SIZE) {
            batches.push(queue.slice(i, i + CONFIG.PROCESSING.BATCH_SIZE));
        }
        return batches;
    }
    
    /**
     * Process a single batch
     */
    async processBatch(batch) {
        const promises = batch.map(item => this.processItem(item));
        
        try {
            const results = await Promise.allSettled(promises);
            
            results.forEach((result, index) => {
                const item = batch[index];
                
                if (result.status === 'fulfilled') {
                    this.results.set(item.id, {
                        ...item,
                        result: result.value,
                        status: 'success',
                        processedAt: Date.now()
                    });
                    this.processedItems.push(item.id);
                } else {
                    this.results.set(item.id, {
                        ...item,
                        error: result.reason.message,
                        status: 'failed',
                        processedAt: Date.now()
                    });
                    this.failedItems.push(item.id);
                }
            });
            
        } catch (error) {
            console.error('Batch processing error:', error);
        }
    }
    
    /**
     * Process a single item
     */
    async processItem(item) {
        try {
            // Build prompt
            const prompt = this.buildPrompt(item.content, item.customPrompt);
            
            // Make API request
            const result = await apiManager.makeRequest(prompt, {
                maxTokens: CONFIG.GEMINI_API.MAX_TOKENS,
                temperature: 0.7
            });
            
            // Clean and validate result
            const cleanedResult = this.cleanResult(result);
            
            return cleanedResult;
            
        } catch (error) {
            console.error(`Error processing item ${item.id}:`, error);
            throw error;
        }
    }
    
    /**
     * Build prompt for AI processing
     */
    buildPrompt(content, customPrompt = '') {
        let prompt = CONFIG.PROMPTS.BASE_PROMPT;
        
        // Add style-specific instructions
        if (this.settings.designStyle && CONFIG.PROMPTS.STYLE_PROMPTS[this.settings.designStyle]) {
            prompt += '\n\n' + CONFIG.PROMPTS.STYLE_PROMPTS[this.settings.designStyle];
        }
        
        // Add language-specific instructions
        if (this.settings.contentLanguage && CONFIG.PROMPTS.LANGUAGE_PROMPTS[this.settings.contentLanguage]) {
            prompt += '\n\n' + CONFIG.PROMPTS.LANGUAGE_PROMPTS[this.settings.contentLanguage];
        }
        
        // Add custom prompt if provided
        if (customPrompt && customPrompt.trim().length > 0) {
            prompt += '\n\nتعليمات إضافية:\n' + customPrompt.trim();
        }
        
        // Add the content to process
        prompt += '\n\n---\n\n' + content;
        
        return prompt;
    }
    
    /**
     * Clean and validate AI result
     */
    cleanResult(result) {
        if (!result || typeof result !== 'string') {
            throw new Error('نتيجة غير صالحة من الذكاء الصناعي');
        }
        
        // Remove any AI-generated comments or explanations
        let cleaned = result.trim();
        
        // Ensure it's valid HTML
        if (!cleaned.includes('<!DOCTYPE html>')) {
            throw new Error('النتيجة لا تحتوي على HTML صالح');
        }
        
        // Sanitize HTML
        cleaned = UTILS.sanitizeHtml(cleaned);
        
        return cleaned;
    }
    
    /**
     * Chunk large content
     */
    chunkContent(content) {
        const chunks = [];
        const maxSize = CONFIG.PROCESSING.MAX_CHUNK_SIZE;
        const overlap = CONFIG.PROCESSING.OVERLAP_SIZE;
        
        if (content.length <= maxSize) {
            return [content];
        }
        
        let start = 0;
        while (start < content.length) {
            let end = start + maxSize;
            
            // Try to break at a natural boundary (sentence, paragraph, etc.)
            if (end < content.length) {
                const breakPoints = ['\n\n', '. ', '! ', '? ', '\n'];
                let bestBreak = end;
                
                for (const breakPoint of breakPoints) {
                    const lastBreak = content.lastIndexOf(breakPoint, end);
                    if (lastBreak > start + maxSize * 0.7) {
                        bestBreak = lastBreak + breakPoint.length;
                        break;
                    }
                }
                
                end = bestBreak;
            }
            
            chunks.push(content.substring(start, end));
            start = Math.max(start + 1, end - overlap);
        }
        
        return chunks;
    }
    
    /**
     * Combine results from chunked content
     */
    async combineChunkedResults() {
        const chunkedItems = new Map();
        
        // Group chunks by original ID
        this.results.forEach((result, id) => {
            if (result.isChunk) {
                const originalId = result.originalId;
                if (!chunkedItems.has(originalId)) {
                    chunkedItems.set(originalId, []);
                }
                chunkedItems.get(originalId).push(result);
            }
        });
        
        // Combine chunks for each original item
        for (const [originalId, chunks] of chunkedItems) {
            if (chunks.length > 1) {
                try {
                    const combinedResult = await this.combineChunks(chunks);
                    
                    // Replace individual chunks with combined result
                    chunks.forEach(chunk => this.results.delete(chunk.id));
                    this.results.set(originalId, {
                        id: originalId,
                        originalId: originalId,
                        result: combinedResult,
                        status: 'success',
                        processedAt: Date.now(),
                        wasCombined: true,
                        chunkCount: chunks.length
                    });
                    
                } catch (error) {
                    console.error(`Error combining chunks for ${originalId}:`, error);
                }
            }
        }
    }
    
    /**
     * Combine multiple chunks into a single result
     */
    async combineChunks(chunks) {
        // Sort chunks by index
        chunks.sort((a, b) => a.chunkIndex - b.chunkIndex);
        
        // Extract content from each chunk
        const contents = chunks.map(chunk => {
            if (chunk.status === 'success' && chunk.result) {
                // Extract body content from HTML
                const bodyMatch = chunk.result.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
                return bodyMatch ? bodyMatch[1] : chunk.result;
            }
            return '';
        }).filter(content => content.length > 0);
        
        if (contents.length === 0) {
            throw new Error('لا توجد محتويات صالحة للدمج');
        }
        
        // Combine contents
        const combinedContent = contents.join('\n\n');
        
        // Create a new HTML document with combined content
        const template = CONFIG.TEMPLATES.HTML_WRAPPER;
        const result = template
            .replace('{{TITLE}}', 'صفحة مدمجة')
            .replace('{{DESCRIPTION}}', 'صفحة HTML مدمجة من عدة أجزاء')
            .replace('{{CSS}}', this.getDefaultCSS())
            .replace('{{CONTENT}}', combinedContent);
        
        return result;
    }
    
    /**
     * Get default CSS for combined pages
     */
    getDefaultCSS() {
        return `
            body {
                font-family: 'Cairo', 'Tajawal', sans-serif;
                direction: rtl;
                text-align: right;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                background: #f8f9fa;
                color: #333;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
        `;
    }
    
    /**
     * Pause processing
     */
    pauseProcessing() {
        this.isPaused = true;
        this.updateProgress(null, 'تم إيقاف المعالجة مؤقتاً', 'يمكنك استئناف المعالجة في أي وقت');
    }
    
    /**
     * Resume processing
     */
    resumeProcessing() {
        this.isPaused = false;
        this.updateProgress(null, 'تم استئناف المعالجة', 'جاري متابعة المعالجة...');
    }
    
    /**
     * Stop processing
     */
    stopProcessing() {
        this.isProcessing = false;
        this.isPaused = false;
        this.updateProgress(null, 'تم إيقاف المعالجة', 'تم إيقاف العملية بواسطة المستخدم');
    }
    
    /**
     * Get processing results
     */
    getResults() {
        const results = Array.from(this.results.values());
        const successful = results.filter(r => r.status === 'success');
        const failed = results.filter(r => r.status === 'failed');
        
        return {
            total: results.length,
            successful: successful.length,
            failed: failed.length,
            results: successful,
            errors: failed,
            processingTime: this.startTime ? Date.now() - this.startTime : 0,
            successRate: results.length > 0 ? (successful.length / results.length) * 100 : 0
        };
    }
    
    /**
     * Update progress in UI
     */
    updateProgress(percentage, title, description) {
        // This will be handled by progress manager
        if (window.progressManager) {
            window.progressManager.updateProgress(percentage, title, description);
        }
    }
    
    /**
     * Show progress section
     */
    showProgressSection() {
        const progressSection = document.getElementById('progressSection');
        if (progressSection) {
            progressSection.style.display = 'block';
        }
    }
    
    /**
     * Handle processing errors
     */
    handleProcessingError(error) {
        console.error('Processing error:', error);
        this.updateProgress(null, 'حدث خطأ في المعالجة', error.message);
    }
    
    /**
     * Utility delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * Get processing statistics
     */
    getStatistics() {
        const results = this.getResults();
        const avgProcessingTime = results.total > 0 ? results.processingTime / results.total : 0;
        
        return {
            ...results,
            averageProcessingTime: avgProcessingTime,
            itemsPerMinute: results.processingTime > 0 ? (results.total / (results.processingTime / 60000)) : 0,
            estimatedTimeRemaining: this.processingQueue.length > 0 ? 
                (this.processingQueue.length - this.processedItems.length) * avgProcessingTime : 0
        };
    }
}

// Create global instance
window.aiProcessor = new AIProcessor();
