/**
 * Excel Processor for reading and processing Excel files
 * Handles large files with chunking and validation
 */

class ExcelProcessor {
    constructor() {
        this.currentFile = null;
        this.processedData = [];
        this.isProcessing = false;
        this.validationErrors = [];
    }
    
    /**
     * Process uploaded Excel file
     */
    async processFile(file) {
        try {
            this.isProcessing = true;
            this.validationErrors = [];
            
            // Validate file
            if (!this.validateFile(file)) {
                throw new Error('ملف غير صالح');
            }
            
            // Show loading state
            this.showLoadingState();
            
            // Read file
            const data = await this.readExcelFile(file);
            
            // Process data
            const processedData = this.processExcelData(data);
            
            // Validate processed data
            this.validateProcessedData(processedData);
            
            // Store results
            this.processedData = processedData;
            this.currentFile = file;
            
            // Update UI
            this.updateFilePreview(processedData);
            this.enableProcessButton();
            
            // Show success message
            this.showSuccessMessage(`تم تحميل الملف بنجاح. تم العثور على ${processedData.length} خلية.`);
            
            return processedData;
            
        } catch (error) {
            this.showErrorMessage(error.message);
            throw error;
        } finally {
            this.isProcessing = false;
            this.hideLoadingState();
        }
    }
    
    /**
     * Process manual text input
     */
    processManualInput(text) {
        try {
            if (!text || text.trim().length === 0) {
                throw new Error('يرجى إدخال نص للمعالجة');
            }
            
            // Split text into lines (each line represents a cell)
            const lines = text.split('\n').filter(line => line.trim().length > 0);
            
            if (lines.length === 0) {
                throw new Error('لم يتم العثور على محتوى صالح');
            }
            
            // Create data structure similar to Excel processing
            const processedData = lines.map((line, index) => ({
                id: UTILS.generateId(),
                cellReference: `A${index + 1}`,
                content: line.trim(),
                originalContent: line.trim(),
                hasHtml: this.detectHtmlContent(line.trim()),
                wordCount: this.countWords(line.trim()),
                charCount: line.trim().length,
                isEmpty: false,
                rowIndex: index + 1,
                columnIndex: 1,
                sheetName: 'Manual Input'
            }));
            
            // Validate processed data
            this.validateProcessedData(processedData);
            
            // Store results
            this.processedData = processedData;
            this.currentFile = null;
            
            // Update UI
            this.updateFilePreview(processedData);
            this.enableProcessButton();
            
            // Show success message
            this.showSuccessMessage(`تم معالجة النص بنجاح. تم العثور على ${processedData.length} عنصر.`);
            
            return processedData;
            
        } catch (error) {
            this.showErrorMessage(error.message);
            throw error;
        }
    }
    
    /**
     * Validate uploaded file
     */
    validateFile(file) {
        // Check file size
        if (file.size > CONFIG.PROCESSING.MAX_FILE_SIZE) {
            this.validationErrors.push(CONFIG.ERRORS.FILE_TOO_LARGE);
            return false;
        }
        
        // Check file format
        const fileName = file.name.toLowerCase();
        const isValidFormat = CONFIG.PROCESSING.SUPPORTED_FORMATS.some(format => 
            fileName.endsWith(format)
        );
        
        if (!isValidFormat) {
            this.validationErrors.push(CONFIG.ERRORS.INVALID_FORMAT);
            return false;
        }
        
        return true;
    }
    
    /**
     * Read Excel file using SheetJS
     */
    async readExcelFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    resolve(workbook);
                } catch (error) {
                    reject(new Error('فشل في قراءة ملف Excel: ' + error.message));
                }
            };
            
            reader.onerror = () => {
                reject(new Error('فشل في قراءة الملف'));
            };
            
            reader.readAsArrayBuffer(file);
        });
    }
    
    /**
     * Process Excel workbook data
     */
    processExcelData(workbook) {
        const processedData = [];
        let globalIndex = 0;
        
        // Process each worksheet
        workbook.SheetNames.forEach(sheetName => {
            const worksheet = workbook.Sheets[sheetName];
            const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
            
            // Process each cell in the worksheet
            for (let row = range.s.r; row <= range.e.r; row++) {
                for (let col = range.s.c; col <= range.e.c; col++) {
                    const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                    const cell = worksheet[cellAddress];
                    
                    if (cell && cell.v !== undefined && cell.v !== null) {
                        const content = this.extractCellContent(cell);
                        
                        if (content && content.trim().length > 0) {
                            const cellData = {
                                id: UTILS.generateId(),
                                cellReference: cellAddress,
                                content: content.trim(),
                                originalContent: content.trim(),
                                hasHtml: this.detectHtmlContent(content),
                                wordCount: this.countWords(content),
                                charCount: content.length,
                                isEmpty: false,
                                rowIndex: row + 1,
                                columnIndex: col + 1,
                                sheetName: sheetName,
                                cellType: cell.t || 's',
                                globalIndex: globalIndex++
                            };
                            
                            processedData.push(cellData);
                        }
                    }
                }
            }
        });
        
        return processedData;
    }
    
    /**
     * Extract content from Excel cell
     */
    extractCellContent(cell) {
        if (!cell || cell.v === undefined || cell.v === null) {
            return '';
        }
        
        // Handle different cell types
        switch (cell.t) {
            case 's': // String
            case 'str': // String
                return String(cell.v);
            case 'n': // Number
                return String(cell.v);
            case 'b': // Boolean
                return cell.v ? 'صحيح' : 'خطأ';
            case 'd': // Date
                return cell.v.toLocaleDateString('ar-SA');
            case 'e': // Error
                return '';
            default:
                return String(cell.v);
        }
    }
    
    /**
     * Detect HTML content in text
     */
    detectHtmlContent(text) {
        const htmlPattern = /<[^>]+>/;
        return htmlPattern.test(text);
    }
    
    /**
     * Count words in text
     */
    countWords(text) {
        if (!text || text.trim().length === 0) {
            return 0;
        }
        
        // Remove HTML tags for word counting
        const cleanText = text.replace(/<[^>]+>/g, ' ');
        
        // Split by whitespace and filter empty strings
        const words = cleanText.trim().split(/\s+/).filter(word => word.length > 0);
        
        return words.length;
    }
    
    /**
     * Validate processed data
     */
    validateProcessedData(data) {
        if (!data || data.length === 0) {
            throw new Error('لم يتم العثور على بيانات صالحة في الملف');
        }
        
        if (data.length > CONFIG.VALIDATION.MAX_CELLS_COUNT) {
            throw new Error(`عدد الخلايا كبير جداً. الحد الأقصى المسموح هو ${CONFIG.VALIDATION.MAX_CELLS_COUNT} خلية`);
        }
        
        // Validate individual cells
        data.forEach((cell, index) => {
            if (!UTILS.validateTextLength(cell.content)) {
                this.validationErrors.push(`الخلية ${cell.cellReference}: طول النص غير صالح`);
            }
        });
        
        if (this.validationErrors.length > 0) {
            throw new Error('تم العثور على أخطاء في التحقق من صحة البيانات');
        }
    }
    
    /**
     * Update file preview in UI
     */
    updateFilePreview(data) {
        const previewContainer = document.getElementById('filePreview');
        if (!previewContainer) return;
        
        const fileInfo = this.currentFile ? {
            name: this.currentFile.name,
            size: UTILS.formatFileSize(this.currentFile.size),
            type: this.currentFile.type
        } : {
            name: 'إدخال يدوي',
            size: UTILS.formatFileSize(data.reduce((sum, item) => sum + item.charCount, 0)),
            type: 'text/plain'
        };
        
        const previewHtml = `
            <div class="file-preview animate-fadeIn">
                <div class="file-preview-header">
                    <div class="file-info">
                        <i class="fas fa-file-excel file-icon"></i>
                        <div class="file-details">
                            <h6>${fileInfo.name}</h6>
                            <p>الحجم: ${fileInfo.size} | العناصر: ${data.length}</p>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="excelProcessor.showDetailedPreview()">
                            <i class="fas fa-eye me-1"></i> عرض تفصيلي
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="excelProcessor.clearFile()">
                            <i class="fas fa-times me-1"></i> إزالة
                        </button>
                    </div>
                </div>
                <div class="file-content">
                    ${this.generatePreviewContent(data)}
                </div>
                ${this.validationErrors.length > 0 ? this.generateErrorsHtml() : ''}
            </div>
        `;
        
        previewContainer.innerHTML = previewHtml;
        previewContainer.style.display = 'block';
    }
    
    /**
     * Generate preview content HTML
     */
    generatePreviewContent(data) {
        const maxPreviewItems = Math.min(data.length, CONFIG.UI.MAX_PREVIEW_LINES);
        let html = '<div class="preview-items">';
        
        for (let i = 0; i < maxPreviewItems; i++) {
            const item = data[i];
            const preview = item.content.length > 100 ? 
                item.content.substring(0, 100) + '...' : 
                item.content;
            
            html += `
                <div class="preview-item">
                    <div class="preview-item-header">
                        <span class="cell-ref">${item.cellReference}</span>
                        <span class="word-count">${item.wordCount} كلمة</span>
                        ${item.hasHtml ? '<span class="badge badge-info">HTML</span>' : ''}
                    </div>
                    <div class="preview-item-content">${preview}</div>
                </div>
            `;
        }
        
        if (data.length > maxPreviewItems) {
            html += `
                <div class="preview-more">
                    <p>و ${data.length - maxPreviewItems} عنصر إضافي...</p>
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    }
    
    /**
     * Generate validation errors HTML
     */
    generateErrorsHtml() {
        if (this.validationErrors.length === 0) return '';
        
        let html = '<div class="validation-errors alert alert-warning">';
        html += '<h6><i class="fas fa-exclamation-triangle me-2"></i>تحذيرات التحقق:</h6>';
        html += '<ul>';
        
        this.validationErrors.forEach(error => {
            html += `<li>${error}</li>`;
        });
        
        html += '</ul></div>';
        return html;
    }
    
    /**
     * Show detailed preview modal
     */
    showDetailedPreview() {
        // Implementation for detailed preview modal
        console.log('Showing detailed preview for', this.processedData.length, 'items');
    }
    
    /**
     * Clear current file and data
     */
    clearFile() {
        this.currentFile = null;
        this.processedData = [];
        this.validationErrors = [];
        
        // Clear UI
        const previewContainer = document.getElementById('filePreview');
        if (previewContainer) {
            previewContainer.style.display = 'none';
            previewContainer.innerHTML = '';
        }
        
        // Reset file input
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.value = '';
        }
        
        // Reset manual input
        const manualInput = document.getElementById('manualInput');
        if (manualInput) {
            manualInput.value = '';
        }
        
        // Disable process button
        this.disableProcessButton();
        
        this.showSuccessMessage('تم مسح البيانات بنجاح');
    }
    
    /**
     * Get processed data
     */
    getProcessedData() {
        return this.processedData;
    }
    
    /**
     * Check if data is ready for processing
     */
    isDataReady() {
        return this.processedData.length > 0 && !this.isProcessing;
    }
    
    /**
     * UI Helper Methods
     */
    showLoadingState() {
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.classList.add('loading');
        }
    }
    
    hideLoadingState() {
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.classList.remove('loading');
        }
    }
    
    enableProcessButton() {
        const processBtn = document.getElementById('startProcessBtn');
        if (processBtn) {
            processBtn.disabled = false;
        }
    }
    
    disableProcessButton() {
        const processBtn = document.getElementById('startProcessBtn');
        if (processBtn) {
            processBtn.disabled = true;
        }
    }
    
    showSuccessMessage(message) {
        // Implementation depends on notification system
        console.log('Success:', message);
    }
    
    showErrorMessage(message) {
        // Implementation depends on notification system
        console.error('Error:', message);
    }
}

// Create global instance
window.excelProcessor = new ExcelProcessor();
