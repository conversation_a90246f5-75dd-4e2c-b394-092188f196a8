/**
 * Export Manager for handling various export formats
 * Supports HTML, Excel, PDF, TXT, ZIP, and settings export
 */

class ExportManager {
    constructor() {
        this.results = [];
        this.isExporting = false;
        this.exportSettings = CONFIG.EXPORT;
        
        // Initialize export handlers
        this.initializeHandlers();
    }
    
    /**
     * Initialize export handlers
     */
    initializeHandlers() {
        // Bind export button events
        this.bindExportEvents();
    }
    
    /**
     * Bind export button events
     */
    bindExportEvents() {
        const exportButtons = {
            'exportHtmlBtn': () => this.exportHTML(),
            'exportExcelBtn': () => this.exportExcel(),
            'exportPdfBtn': () => this.exportPDF(),
            'exportTxtBtn': () => this.exportTXT(),
            'exportZipBtn': () => this.exportZIP(),
            'exportSettingsBtn': () => this.exportSettings()
        };
        
        Object.entries(exportButtons).forEach(([buttonId, handler]) => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.addEventListener('click', handler);
            }
        });
    }
    
    /**
     * Set results data for export
     */
    setResults(results) {
        this.results = Array.isArray(results) ? results : [];
    }
    
    /**
     * Export as HTML with search functionality
     */
    async exportHTML() {
        try {
            this.showExportProgress('جاري تصدير HTML...');
            
            if (!this.results || this.results.length === 0) {
                throw new Error('لا توجد نتائج للتصدير');
            }
            
            // Generate HTML content
            const htmlContent = this.generateHTMLExport();
            
            // Create and download file
            this.downloadFile(htmlContent, 'excel-to-html-results.html', 'text/html');
            
            this.showExportSuccess('تم تصدير HTML بنجاح');
            
        } catch (error) {
            this.showExportError('فشل في تصدير HTML: ' + error.message);
        } finally {
            this.hideExportProgress();
        }
    }
    
    /**
     * Generate HTML export with search functionality
     */
    generateHTMLExport() {
        const searchScript = this.generateSearchScript();
        const searchCSS = this.generateSearchCSS();
        
        let htmlContent = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج تحويل Excel إلى HTML</title>
    <meta name="description" content="نتائج تحويل ملف Excel إلى صفحات HTML منسقة">
    <style>
        ${searchCSS}
    </style>
</head>
<body>
    <div class="container">
        <header class="main-header">
            <h1>نتائج تحويل Excel إلى HTML</h1>
            <p>تم إنشاء ${this.results.length} صفحة HTML من ملف Excel</p>
            <div class="export-info">
                <span>تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}</span>
                <span>الوقت: ${new Date().toLocaleTimeString('ar-SA')}</span>
            </div>
        </header>
        
        <div class="search-section">
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="البحث في المحتوى..." class="search-input">
                <button onclick="clearSearch()" class="clear-btn">مسح</button>
            </div>
            <div class="search-stats" id="searchStats"></div>
        </div>
        
        <div class="results-container" id="resultsContainer">
            ${this.generateResultsHTML()}
        </div>
        
        <footer class="main-footer">
            <p>تم إنشاؤه بواسطة أداة تحويل Excel إلى HTML بالذكاء الصناعي</p>
        </footer>
    </div>
    
    <script>
        ${searchScript}
    </script>
</body>
</html>`;
        
        return htmlContent;
    }
    
    /**
     * Generate results HTML
     */
    generateResultsHTML() {
        return this.results.map((result, index) => {
            const originalItem = result.originalItem || {};
            return `
                <div class="result-item" data-index="${index}">
                    <div class="result-header">
                        <h3>صفحة ${index + 1}</h3>
                        <div class="result-meta">
                            <span class="cell-ref">${originalItem.cellReference || 'N/A'}</span>
                            <span class="word-count">${originalItem.wordCount || 0} كلمة</span>
                            ${originalItem.hasHtml ? '<span class="badge">HTML أصلي</span>' : ''}
                        </div>
                    </div>
                    <div class="result-content">
                        <iframe srcdoc="${this.escapeHtml(result.result)}" class="result-frame"></iframe>
                    </div>
                    <div class="result-actions">
                        <button onclick="viewFullscreen(${index})" class="btn btn-primary">عرض كامل</button>
                        <button onclick="downloadSingle(${index})" class="btn btn-secondary">تحميل</button>
                    </div>
                </div>
            `;
        }).join('');
    }
    
    /**
     * Generate search CSS
     */
    generateSearchCSS() {
        return `
            * { box-sizing: border-box; }
            body {
                font-family: 'Cairo', 'Tajawal', sans-serif;
                margin: 0;
                padding: 0;
                background: #f8f9fa;
                color: #333;
                line-height: 1.6;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }
            .main-header {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                margin-bottom: 30px;
                text-align: center;
            }
            .main-header h1 {
                color: #2563eb;
                margin-bottom: 10px;
            }
            .export-info {
                margin-top: 15px;
                color: #666;
            }
            .export-info span {
                margin: 0 10px;
            }
            .search-section {
                background: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                margin-bottom: 30px;
            }
            .search-container {
                display: flex;
                gap: 10px;
                margin-bottom: 15px;
            }
            .search-input {
                flex: 1;
                padding: 12px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 16px;
            }
            .search-input:focus {
                border-color: #2563eb;
                outline: none;
            }
            .clear-btn {
                padding: 12px 20px;
                background: #dc2626;
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
            }
            .search-stats {
                color: #666;
                font-size: 14px;
            }
            .results-container {
                display: grid;
                gap: 30px;
            }
            .result-item {
                background: white;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
                transition: transform 0.3s ease;
            }
            .result-item:hover {
                transform: translateY(-5px);
            }
            .result-item.hidden {
                display: none;
            }
            .result-header {
                padding: 20px;
                background: #f8f9fa;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .result-header h3 {
                margin: 0;
                color: #2563eb;
            }
            .result-meta {
                display: flex;
                gap: 15px;
                align-items: center;
            }
            .cell-ref {
                background: #e3f2fd;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
            .word-count {
                color: #666;
                font-size: 14px;
            }
            .badge {
                background: #059669;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
            }
            .result-content {
                padding: 20px;
            }
            .result-frame {
                width: 100%;
                height: 400px;
                border: 1px solid #ddd;
                border-radius: 8px;
            }
            .result-actions {
                padding: 20px;
                background: #f8f9fa;
                display: flex;
                gap: 10px;
            }
            .btn {
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 500;
                transition: background 0.3s ease;
            }
            .btn-primary {
                background: #2563eb;
                color: white;
            }
            .btn-primary:hover {
                background: #1d4ed8;
            }
            .btn-secondary {
                background: #6b7280;
                color: white;
            }
            .btn-secondary:hover {
                background: #4b5563;
            }
            .main-footer {
                text-align: center;
                padding: 30px;
                color: #666;
                margin-top: 50px;
            }
            .highlight {
                background: yellow;
                padding: 2px 4px;
                border-radius: 3px;
            }
        `;
    }
    
    /**
     * Generate search script
     */
    generateSearchScript() {
        return `
            let allResults = [];
            let filteredResults = [];
            
            // Initialize search
            document.addEventListener('DOMContentLoaded', function() {
                allResults = Array.from(document.querySelectorAll('.result-item'));
                filteredResults = [...allResults];
                
                const searchInput = document.getElementById('searchInput');
                searchInput.addEventListener('input', performSearch);
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        performSearch();
                    }
                });
                
                updateSearchStats();
            });
            
            function performSearch() {
                const query = document.getElementById('searchInput').value.trim().toLowerCase();
                
                if (!query) {
                    showAllResults();
                    return;
                }
                
                filteredResults = allResults.filter(item => {
                    const content = item.textContent.toLowerCase();
                    return content.includes(query);
                });
                
                showFilteredResults();
                highlightSearchTerms(query);
                updateSearchStats();
            }
            
            function showAllResults() {
                allResults.forEach(item => {
                    item.classList.remove('hidden');
                    removeHighlights(item);
                });
                filteredResults = [...allResults];
                updateSearchStats();
            }
            
            function showFilteredResults() {
                allResults.forEach(item => {
                    if (filteredResults.includes(item)) {
                        item.classList.remove('hidden');
                    } else {
                        item.classList.add('hidden');
                    }
                });
            }
            
            function highlightSearchTerms(query) {
                filteredResults.forEach(item => {
                    const textNodes = getTextNodes(item);
                    textNodes.forEach(node => {
                        const text = node.textContent;
                        const regex = new RegExp(\`(\${escapeRegex(query)})\`, 'gi');
                        if (regex.test(text)) {
                            const highlightedText = text.replace(regex, '<span class="highlight">$1</span>');
                            const wrapper = document.createElement('div');
                            wrapper.innerHTML = highlightedText;
                            node.parentNode.replaceChild(wrapper, node);
                        }
                    });
                });
            }
            
            function removeHighlights(item) {
                const highlights = item.querySelectorAll('.highlight');
                highlights.forEach(highlight => {
                    const parent = highlight.parentNode;
                    parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                    parent.normalize();
                });
            }
            
            function getTextNodes(element) {
                const textNodes = [];
                const walker = document.createTreeWalker(
                    element,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                let node;
                while (node = walker.nextNode()) {
                    if (node.textContent.trim()) {
                        textNodes.push(node);
                    }
                }
                
                return textNodes;
            }
            
            function escapeRegex(string) {
                return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');
            }
            
            function updateSearchStats() {
                const statsEl = document.getElementById('searchStats');
                const query = document.getElementById('searchInput').value.trim();
                
                if (query) {
                    statsEl.textContent = \`تم العثور على \${filteredResults.length} من أصل \${allResults.length} نتيجة\`;
                } else {
                    statsEl.textContent = \`إجمالي النتائج: \${allResults.length}\`;
                }
            }
            
            function clearSearch() {
                document.getElementById('searchInput').value = '';
                showAllResults();
            }
            
            function viewFullscreen(index) {
                const result = ${JSON.stringify(this.results)}[index];
                if (result) {
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(result.result);
                    newWindow.document.close();
                }
            }
            
            function downloadSingle(index) {
                const result = ${JSON.stringify(this.results)}[index];
                if (result) {
                    const blob = new Blob([result.result], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = \`page-\${index + 1}.html\`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }
            }
        `;
    }
    
    /**
     * Export as Excel
     */
    async exportExcel() {
        try {
            this.showExportProgress('جاري تصدير Excel...');
            
            if (!this.results || this.results.length === 0) {
                throw new Error('لا توجد نتائج للتصدير');
            }
            
            // Create workbook
            const wb = XLSX.utils.book_new();
            
            // Prepare data
            const data = this.results.map((result, index) => {
                const originalItem = result.originalItem || {};
                return {
                    'رقم الصفحة': index + 1,
                    'مرجع الخلية': originalItem.cellReference || 'N/A',
                    'المحتوى الأصلي': originalItem.content || '',
                    'عدد الكلمات': originalItem.wordCount || 0,
                    'عدد الأحرف': originalItem.charCount || 0,
                    'يحتوي على HTML': originalItem.hasHtml ? 'نعم' : 'لا',
                    'حالة المعالجة': result.status === 'success' ? 'نجح' : 'فشل',
                    'تاريخ المعالجة': new Date(result.processedAt).toLocaleString('ar-SA')
                };
            });
            
            // Create worksheet
            const ws = XLSX.utils.json_to_sheet(data);
            
            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, this.exportSettings.EXCEL.SHEET_NAME);
            
            // Generate Excel file
            const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
            
            // Download file
            this.downloadBlob(
                new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }),
                'excel-to-html-results.xlsx'
            );
            
            this.showExportSuccess('تم تصدير Excel بنجاح');
            
        } catch (error) {
            this.showExportError('فشل في تصدير Excel: ' + error.message);
        } finally {
            this.hideExportProgress();
        }
    }
    
    /**
     * Export as PDF
     */
    async exportPDF() {
        try {
            this.showExportProgress('جاري تصدير PDF...');
            
            if (!this.results || this.results.length === 0) {
                throw new Error('لا توجد نتائج للتصدير');
            }
            
            // Create PDF using jsPDF
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF({
                orientation: this.exportSettings.PDF.ORIENTATION,
                unit: 'mm',
                format: this.exportSettings.PDF.FORMAT
            });
            
            // Add Arabic font support (if available)
            // Note: This would require additional font files
            
            let yPosition = 20;
            const pageHeight = doc.internal.pageSize.height;
            const margin = this.exportSettings.PDF.MARGIN;
            
            // Add title
            doc.setFontSize(20);
            doc.text('نتائج تحويل Excel إلى HTML', margin, yPosition);
            yPosition += 15;
            
            // Add metadata
            doc.setFontSize(12);
            doc.text(`تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}`, margin, yPosition);
            yPosition += 10;
            doc.text(`عدد الصفحات: ${this.results.length}`, margin, yPosition);
            yPosition += 20;
            
            // Add results
            this.results.forEach((result, index) => {
                if (yPosition > pageHeight - 40) {
                    doc.addPage();
                    yPosition = 20;
                }
                
                const originalItem = result.originalItem || {};
                
                doc.setFontSize(14);
                doc.text(`صفحة ${index + 1}`, margin, yPosition);
                yPosition += 8;
                
                doc.setFontSize(10);
                doc.text(`مرجع الخلية: ${originalItem.cellReference || 'N/A'}`, margin, yPosition);
                yPosition += 6;
                doc.text(`عدد الكلمات: ${originalItem.wordCount || 0}`, margin, yPosition);
                yPosition += 6;
                
                // Add content preview (first 200 characters)
                const contentPreview = (originalItem.content || '').substring(0, 200) + '...';
                const lines = doc.splitTextToSize(contentPreview, 170);
                doc.text(lines, margin, yPosition);
                yPosition += lines.length * 5 + 10;
            });
            
            // Save PDF
            doc.save('excel-to-html-results.pdf');
            
            this.showExportSuccess('تم تصدير PDF بنجاح');
            
        } catch (error) {
            this.showExportError('فشل في تصدير PDF: ' + error.message);
        } finally {
            this.hideExportProgress();
        }
    }
    
    /**
     * Export as TXT
     */
    async exportTXT() {
        try {
            this.showExportProgress('جاري تصدير النص...');
            
            if (!this.results || this.results.length === 0) {
                throw new Error('لا توجد نتائج للتصدير');
            }
            
            let txtContent = 'نتائج تحويل Excel إلى HTML\n';
            txtContent += '================================\n\n';
            txtContent += `تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}\n`;
            txtContent += `عدد الصفحات: ${this.results.length}\n\n`;
            
            this.results.forEach((result, index) => {
                const originalItem = result.originalItem || {};
                
                txtContent += `صفحة ${index + 1}\n`;
                txtContent += `----------\n`;
                txtContent += `مرجع الخلية: ${originalItem.cellReference || 'N/A'}\n`;
                txtContent += `عدد الكلمات: ${originalItem.wordCount || 0}\n`;
                txtContent += `عدد الأحرف: ${originalItem.charCount || 0}\n`;
                txtContent += `يحتوي على HTML: ${originalItem.hasHtml ? 'نعم' : 'لا'}\n`;
                txtContent += `حالة المعالجة: ${result.status === 'success' ? 'نجح' : 'فشل'}\n\n`;
                txtContent += 'المحتوى الأصلي:\n';
                txtContent += originalItem.content || '';
                txtContent += '\n\n';
                txtContent += '================================\n\n';
            });
            
            // Download file
            this.downloadFile(txtContent, 'excel-to-html-results.txt', 'text/plain');
            
            this.showExportSuccess('تم تصدير النص بنجاح');
            
        } catch (error) {
            this.showExportError('فشل في تصدير النص: ' + error.message);
        } finally {
            this.hideExportProgress();
        }
    }
    
    /**
     * Export as ZIP containing all formats
     */
    async exportZIP() {
        try {
            this.showExportProgress('جاري إنشاء الملف المضغوط...');
            
            if (!this.results || this.results.length === 0) {
                throw new Error('لا توجد نتائج للتصدير');
            }
            
            const zip = new JSZip();
            
            // Add HTML export
            const htmlContent = this.generateHTMLExport();
            zip.file('results.html', htmlContent);
            
            // Add individual HTML files
            const htmlFolder = zip.folder('html-pages');
            this.results.forEach((result, index) => {
                htmlFolder.file(`page-${index + 1}.html`, result.result);
            });
            
            // Add Excel export
            const wb = XLSX.utils.book_new();
            const data = this.results.map((result, index) => {
                const originalItem = result.originalItem || {};
                return {
                    'رقم الصفحة': index + 1,
                    'مرجع الخلية': originalItem.cellReference || 'N/A',
                    'المحتوى الأصلي': originalItem.content || '',
                    'عدد الكلمات': originalItem.wordCount || 0,
                    'عدد الأحرف': originalItem.charCount || 0,
                    'يحتوي على HTML': originalItem.hasHtml ? 'نعم' : 'لا',
                    'حالة المعالجة': result.status === 'success' ? 'نجح' : 'فشل'
                };
            });
            const ws = XLSX.utils.json_to_sheet(data);
            XLSX.utils.book_append_sheet(wb, ws, 'النتائج');
            const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
            zip.file('results.xlsx', excelBuffer);
            
            // Generate and download ZIP
            const zipBlob = await zip.generateAsync({ type: 'blob' });
            this.downloadBlob(zipBlob, 'excel-to-html-complete.zip');
            
            this.showExportSuccess('تم إنشاء الملف المضغوط بنجاح');
            
        } catch (error) {
            this.showExportError('فشل في إنشاء الملف المضغوط: ' + error.message);
        } finally {
            this.hideExportProgress();
        }
    }
    
    /**
     * Export settings
     */
    exportSettings() {
        try {
            const settings = this.getCurrentSettings();
            const settingsJson = JSON.stringify(settings, null, 2);
            
            this.downloadFile(
                settingsJson,
                'excel-to-html-settings.json',
                'application/json'
            );
            
            this.showExportSuccess('تم تصدير الإعدادات بنجاح');
            
        } catch (error) {
            this.showExportError('فشل في تصدير الإعدادات: ' + error.message);
        }
    }
    
    /**
     * Get current settings
     */
    getCurrentSettings() {
        return {
            version: CONFIG.APP_VERSION,
            exportDate: new Date().toISOString(),
            settings: window.settingsManager ? window.settingsManager.getAllSettings() : CONFIG.DEFAULT_SETTINGS,
            apiStatus: window.apiManager ? window.apiManager.getAPIStatus() : null
        };
    }
    
    /**
     * Utility functions
     */
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        this.downloadBlob(blob, filename);
    }
    
    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML.replace(/"/g, '&quot;');
    }
    
    showExportProgress(message) {
        // Implementation depends on UI framework
        console.log('Export Progress:', message);
    }
    
    hideExportProgress() {
        // Implementation depends on UI framework
        console.log('Export Progress: Hidden');
    }
    
    showExportSuccess(message) {
        // Implementation depends on notification system
        console.log('Export Success:', message);
    }
    
    showExportError(message) {
        // Implementation depends on notification system
        console.error('Export Error:', message);
    }
}

// Create global instance
window.exportManager = new ExportManager();
