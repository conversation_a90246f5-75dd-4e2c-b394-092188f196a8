/* Animations and Effects for Excel to HTML AI Converter */

/* Fade In Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scale Animations */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Shake Animation */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

/* Pulse Animation */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Glow Animation */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(37, 99, 235, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(37, 99, 235, 0.8), 0 0 30px rgba(37, 99, 235, 0.6);
    }
}

/* Progress Bar Animation */
@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--progress-width, 0%);
    }
}

/* Typing Animation */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: var(--primary-color);
    }
}

/* Slide Animations */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutLeft {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(-100%);
        opacity: 0;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Flip Animation */
@keyframes flipInX {
    from {
        transform: perspective(400px) rotateX(90deg);
        opacity: 0;
    }
    40% {
        transform: perspective(400px) rotateX(-20deg);
    }
    60% {
        transform: perspective(400px) rotateX(10deg);
        opacity: 1;
    }
    80% {
        transform: perspective(400px) rotateX(-5deg);
    }
    to {
        transform: perspective(400px) rotateX(0deg);
        opacity: 1;
    }
}

/* Rotate Animation */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Animation Classes */
.animate-fadeIn {
    animation: fadeIn 0.6s ease-out;
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInDown {
    animation: fadeInDown 0.6s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s ease-out;
}

.animate-scaleIn {
    animation: scaleIn 0.4s ease-out;
}

.animate-scaleOut {
    animation: scaleOut 0.4s ease-out;
}

.animate-bounce {
    animation: bounce 1s ease-out;
}

.animate-shake {
    animation: shake 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-glow {
    animation: glow 2s infinite;
}

.animate-slideInLeft {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slideInRight {
    animation: slideInRight 0.6s ease-out;
}

.animate-slideOutLeft {
    animation: slideOutLeft 0.6s ease-out;
}

.animate-slideOutRight {
    animation: slideOutRight 0.6s ease-out;
}

.animate-flipInX {
    animation: flipInX 0.8s ease-out;
}

.animate-rotate {
    animation: rotate 1s linear infinite;
}

/* Hover Effects */
.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: var(--transition);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.4);
}

.hover-rotate {
    transition: var(--transition);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* Stagger Animation */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }

/* Progress Animations */
.progress-animated .progress-bar {
    animation: progressFill 2s ease-out;
}

.progress-glow .progress-bar {
    box-shadow: 0 0 10px rgba(37, 99, 235, 0.5);
    animation: glow 2s infinite;
}

/* Text Animations */
.typing-effect {
    overflow: hidden;
    border-left: 2px solid var(--primary-color);
    white-space: nowrap;
    margin: 0 auto;
    animation: typing 3s steps(40, end), blink 0.75s step-end infinite;
}

/* Notification Animations */
.notification-enter {
    animation: slideInRight 0.4s ease-out;
}

.notification-exit {
    animation: slideOutRight 0.4s ease-out;
}

/* Modal Animations */
.modal.fade .modal-dialog {
    transform: scale(0.8);
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: scale(1);
}

/* Button Ripple Effect */
.btn-ripple {
    position: relative;
    overflow: hidden;
}

.btn-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Smooth Transitions */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.smooth-transition-fast {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.smooth-transition-slow {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
