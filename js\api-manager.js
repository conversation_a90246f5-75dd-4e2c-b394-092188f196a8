/**
 * API Manager for Gemini API Keys
 * Handles rotation, rate limiting, and error handling
 */

class APIManager {
    constructor() {
        this.apiKeys = [...CONFIG.GEMINI_API.KEYS];
        this.currentKeyIndex = 0;
        this.keyUsage = new Map();
        this.keyStatus = new Map();
        this.requestQueue = [];
        this.isProcessingQueue = false;
        
        // Initialize key usage tracking
        this.initializeKeys();
        
        // Start monitoring
        this.startMonitoring();
    }
    
    /**
     * Initialize API keys with usage tracking
     */
    initializeKeys() {
        this.apiKeys.forEach((key, index) => {
            this.keyUsage.set(key, {
                requestsToday: 0,
                requestsThisMinute: 0,
                lastRequest: null,
                errors: 0,
                successRate: 100,
                isBlocked: false,
                blockUntil: null
            });
            
            this.keyStatus.set(key, 'active');
        });
        
        // Load usage data from localStorage
        this.loadUsageData();
    }
    
    /**
     * Get next available API key
     */
    getNextAvailableKey() {
        const now = Date.now();
        let attempts = 0;
        
        while (attempts < this.apiKeys.length) {
            const key = this.apiKeys[this.currentKeyIndex];
            const usage = this.keyUsage.get(key);
            
            // Check if key is blocked
            if (usage.isBlocked && usage.blockUntil && now < usage.blockUntil) {
                this.rotateKey();
                attempts++;
                continue;
            }
            
            // Reset block if time has passed
            if (usage.isBlocked && usage.blockUntil && now >= usage.blockUntil) {
                usage.isBlocked = false;
                usage.blockUntil = null;
                this.keyStatus.set(key, 'active');
            }
            
            // Check rate limits
            if (this.isKeyAvailable(key)) {
                return key;
            }
            
            this.rotateKey();
            attempts++;
        }
        
        throw new Error('جميع مفاتيح API غير متاحة حالياً. يرجى المحاولة لاحقاً.');
    }
    
    /**
     * Check if API key is available for use
     */
    isKeyAvailable(key) {
        const usage = this.keyUsage.get(key);
        const now = Date.now();
        
        // Check if blocked
        if (usage.isBlocked) {
            return false;
        }
        
        // Check daily limit
        if (usage.requestsToday >= CONFIG.GEMINI_API.MAX_REQUESTS_PER_DAY) {
            this.blockKey(key, 24 * 60 * 60 * 1000); // Block for 24 hours
            return false;
        }
        
        // Check minute limit
        if (usage.lastRequest && now - usage.lastRequest < 60000) {
            if (usage.requestsThisMinute >= CONFIG.GEMINI_API.MAX_REQUESTS_PER_MINUTE) {
                return false;
            }
        } else {
            // Reset minute counter
            usage.requestsThisMinute = 0;
        }
        
        return true;
    }
    
    /**
     * Make API request with automatic key rotation
     */
    async makeRequest(prompt, options = {}) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({
                prompt,
                options,
                resolve,
                reject,
                timestamp: Date.now()
            });
            
            this.processQueue();
        });
    }
    
    /**
     * Process request queue
     */
    async processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        while (this.requestQueue.length > 0) {
            const request = this.requestQueue.shift();
            
            try {
                const result = await this.executeRequest(request.prompt, request.options);
                request.resolve(result);
            } catch (error) {
                request.reject(error);
            }
            
            // Add delay between requests
            if (this.requestQueue.length > 0) {
                await this.delay(CONFIG.GEMINI_API.RETRY_DELAY);
            }
        }
        
        this.isProcessingQueue = false;
    }
    
    /**
     * Execute single API request
     */
    async executeRequest(prompt, options = {}) {
        let lastError = null;
        let attempts = 0;
        
        while (attempts < CONFIG.GEMINI_API.MAX_RETRIES) {
            try {
                const key = this.getNextAvailableKey();
                const result = await this.callGeminiAPI(key, prompt, options);
                
                // Update usage statistics
                this.updateUsageStats(key, true);
                
                return result;
            } catch (error) {
                lastError = error;
                attempts++;
                
                // Update error statistics
                if (error.key) {
                    this.updateUsageStats(error.key, false, error);
                }
                
                // Wait before retry
                if (attempts < CONFIG.GEMINI_API.MAX_RETRIES) {
                    await this.delay(CONFIG.GEMINI_API.RETRY_DELAY * attempts);
                }
            }
        }
        
        throw lastError || new Error('فشل في تنفيذ الطلب بعد عدة محاولات');
    }
    
    /**
     * Call Gemini API
     */
    async callGeminiAPI(key, prompt, options = {}) {
        const url = `${CONFIG.GEMINI_API.BASE_URL}?key=${key}`;
        
        const requestBody = {
            contents: [{
                parts: [{ text: prompt }]
            }],
            generationConfig: {
                maxOutputTokens: options.maxTokens || CONFIG.GEMINI_API.MAX_TOKENS,
                temperature: options.temperature || 0.7,
                topP: options.topP || 0.8,
                topK: options.topK || 40
            }
        };
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        if (!response.ok) {
            const error = new Error(`API Error: ${response.status} - ${response.statusText}`);
            error.key = key;
            error.status = response.status;
            
            // Handle specific error codes
            if (response.status === 429) {
                this.blockKey(key, 60 * 60 * 1000); // Block for 1 hour
                error.message = 'تم تجاوز حد الطلبات المسموح. سيتم المحاولة بمفتاح آخر.';
            } else if (response.status === 403) {
                this.blockKey(key, 24 * 60 * 60 * 1000); // Block for 24 hours
                error.message = 'مفتاح API غير صالح أو منتهي الصلاحية.';
            }
            
            throw error;
        }
        
        const data = await response.json();
        
        if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
            throw new Error('استجابة غير صالحة من API');
        }
        
        return data.candidates[0].content.parts[0].text;
    }
    
    /**
     * Update usage statistics
     */
    updateUsageStats(key, success, error = null) {
        const usage = this.keyUsage.get(key);
        const now = Date.now();
        
        // Update request counts
        usage.requestsToday++;
        usage.requestsThisMinute++;
        usage.lastRequest = now;
        
        // Update success rate
        if (success) {
            const totalRequests = usage.requestsToday;
            const successfulRequests = Math.floor(totalRequests * (usage.successRate / 100)) + 1;
            usage.successRate = (successfulRequests / totalRequests) * 100;
        } else {
            usage.errors++;
            const totalRequests = usage.requestsToday;
            const successfulRequests = Math.floor(totalRequests * (usage.successRate / 100));
            usage.successRate = (successfulRequests / totalRequests) * 100;
        }
        
        // Save usage data
        this.saveUsageData();
        
        // Update UI
        this.updateAPIStatusUI();
    }
    
    /**
     * Block API key
     */
    blockKey(key, duration) {
        const usage = this.keyUsage.get(key);
        usage.isBlocked = true;
        usage.blockUntil = Date.now() + duration;
        this.keyStatus.set(key, 'blocked');
        
        console.warn(`API Key blocked for ${duration / 1000} seconds`);
        this.updateAPIStatusUI();
    }
    
    /**
     * Rotate to next API key
     */
    rotateKey() {
        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
    }
    
    /**
     * Get API status summary
     */
    getAPIStatus() {
        const status = {
            total: this.apiKeys.length,
            active: 0,
            blocked: 0,
            limited: 0,
            totalRequests: 0,
            averageSuccessRate: 0
        };
        
        let totalSuccessRate = 0;
        
        this.apiKeys.forEach(key => {
            const usage = this.keyUsage.get(key);
            const keyStatus = this.keyStatus.get(key);
            
            status.totalRequests += usage.requestsToday;
            totalSuccessRate += usage.successRate;
            
            if (keyStatus === 'active') {
                if (usage.requestsToday >= CONFIG.GEMINI_API.MAX_REQUESTS_PER_DAY * 0.8) {
                    status.limited++;
                } else {
                    status.active++;
                }
            } else {
                status.blocked++;
            }
        });
        
        status.averageSuccessRate = totalSuccessRate / this.apiKeys.length;
        
        return status;
    }
    
    /**
     * Update API status in UI
     */
    updateAPIStatusUI() {
        const status = this.getAPIStatus();
        
        // Update status indicators if they exist
        const statusContainer = document.getElementById('apiStatusContainer');
        if (statusContainer) {
            statusContainer.innerHTML = this.generateStatusHTML(status);
        }
        
        // Dispatch custom event
        window.dispatchEvent(new CustomEvent('apiStatusUpdate', { detail: status }));
    }
    
    /**
     * Generate status HTML
     */
    generateStatusHTML(status) {
        return `
            <div class="api-status-grid">
                <div class="api-status active">
                    <span>نشط: ${status.active}</span>
                    <div class="api-status-indicator"></div>
                </div>
                <div class="api-status limited">
                    <span>محدود: ${status.limited}</span>
                    <div class="api-status-indicator"></div>
                </div>
                <div class="api-status inactive">
                    <span>محظور: ${status.blocked}</span>
                    <div class="api-status-indicator"></div>
                </div>
                <div class="api-status-summary">
                    <small>إجمالي الطلبات: ${status.totalRequests}</small>
                    <small>معدل النجاح: ${status.averageSuccessRate.toFixed(1)}%</small>
                </div>
            </div>
        `;
    }
    
    /**
     * Start monitoring system
     */
    startMonitoring() {
        // Reset daily counters at midnight
        setInterval(() => {
            const now = new Date();
            if (now.getHours() === 0 && now.getMinutes() === 0) {
                this.resetDailyCounters();
            }
        }, 60000); // Check every minute
        
        // Update UI every 30 seconds
        setInterval(() => {
            this.updateAPIStatusUI();
        }, 30000);
    }
    
    /**
     * Reset daily counters
     */
    resetDailyCounters() {
        this.apiKeys.forEach(key => {
            const usage = this.keyUsage.get(key);
            usage.requestsToday = 0;
            usage.errors = 0;
            usage.successRate = 100;
            
            // Unblock keys if it's a new day
            if (usage.isBlocked) {
                usage.isBlocked = false;
                usage.blockUntil = null;
                this.keyStatus.set(key, 'active');
            }
        });
        
        this.saveUsageData();
        this.updateAPIStatusUI();
    }
    
    /**
     * Save usage data to localStorage
     */
    saveUsageData() {
        const data = {
            keyUsage: Array.from(this.keyUsage.entries()),
            keyStatus: Array.from(this.keyStatus.entries()),
            currentKeyIndex: this.currentKeyIndex,
            lastSave: Date.now()
        };
        
        localStorage.setItem(CONFIG.STORAGE.PREFIX + 'api_usage', JSON.stringify(data));
    }
    
    /**
     * Load usage data from localStorage
     */
    loadUsageData() {
        try {
            const data = localStorage.getItem(CONFIG.STORAGE.PREFIX + 'api_usage');
            if (data) {
                const parsed = JSON.parse(data);
                
                // Check if data is from today
                const lastSave = new Date(parsed.lastSave);
                const today = new Date();
                
                if (lastSave.toDateString() === today.toDateString()) {
                    // Restore usage data
                    this.keyUsage = new Map(parsed.keyUsage);
                    this.keyStatus = new Map(parsed.keyStatus);
                    this.currentKeyIndex = parsed.currentKeyIndex;
                } else {
                    // Reset if it's a new day
                    this.resetDailyCounters();
                }
            }
        } catch (error) {
            console.warn('Failed to load API usage data:', error);
        }
    }
    
    /**
     * Utility function for delays
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * Get detailed key information
     */
    getKeyDetails() {
        return this.apiKeys.map((key, index) => {
            const usage = this.keyUsage.get(key);
            const status = this.keyStatus.get(key);
            
            return {
                index,
                key: key.substring(0, 10) + '...',
                status,
                requestsToday: usage.requestsToday,
                successRate: usage.successRate.toFixed(1),
                errors: usage.errors,
                isBlocked: usage.isBlocked,
                blockUntil: usage.blockUntil
            };
        });
    }
}

// Create global instance
window.apiManager = new APIManager();
