/* Main Styles for Excel to HTML AI Converter */

:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--dark-color);
    line-height: 1.6;
}

/* Header Styles */
.main-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.5rem 0;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

.header-title {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 2rem;
}

.header-subtitle {
    color: var(--secondary-color);
    font-size: 1.1rem;
    margin-bottom: 0;
}

.header-actions .btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: var(--transition);
}

.header-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

/* Main Content */
.main-content {
    padding-bottom: 3rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 2rem;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.card-header {
    border-bottom: none;
    padding: 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 2rem;
}

.card-title {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
}

/* Upload Area */
.upload-area {
    border: 3px dashed var(--primary-color);
    border-radius: var(--border-radius);
    padding: 3rem 2rem;
    text-align: center;
    background: rgba(37, 99, 235, 0.05);
    transition: var(--transition);
    cursor: pointer;
    min-height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-area:hover {
    border-color: var(--success-color);
    background: rgba(5, 150, 105, 0.05);
    transform: scale(1.02);
}

.upload-area.dragover {
    border-color: var(--success-color);
    background: rgba(5, 150, 105, 0.1);
    transform: scale(1.05);
}

.upload-content {
    width: 100%;
}

.upload-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    transition: var(--transition);
}

.upload-area:hover .upload-icon {
    color: var(--success-color);
    transform: scale(1.1);
}

.upload-content h4 {
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.upload-content p {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}

/* Manual Input Area */
.manual-input-area {
    background: var(--light-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.manual-input-area h5 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.manual-input-area textarea {
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-family: 'Tajawal', sans-serif;
}

.manual-input-area textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Form Controls */
.form-control, .form-select {
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    font-family: 'Tajawal', sans-serif;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    font-family: 'Cairo', sans-serif;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #10b981 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #ef4444 100%);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #06b6d4 100%);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Progress Section */
.progress-section {
    background: var(--light-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.progress-info h5 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.progress-info p {
    color: var(--secondary-color);
    margin-bottom: 0;
}

.progress-controls .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.progress {
    height: 1.5rem;
    border-radius: var(--border-radius);
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--success-color) 100%);
    transition: width 0.6s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.progress-details {
    margin-top: 1.5rem;
}

.stat-item {
    background: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--box-shadow);
}

.stat-item h6 {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.stat-item span {
    color: var(--dark-color);
    font-size: 1.2rem;
    font-weight: 700;
}

/* Results Section */
.results-summary {
    background: var(--light-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.result-stat {
    text-align: center;
    padding: 1rem;
}

.result-stat h4 {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.result-stat p {
    color: var(--secondary-color);
    margin-bottom: 0;
    font-weight: 500;
}

.export-options h5 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Footer */
.main-footer {
    background: rgba(30, 41, 59, 0.95);
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
    backdrop-filter: blur(10px);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: white;
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-title {
        font-size: 1.5rem;
    }
    
    .header-subtitle {
        font-size: 1rem;
    }
    
    .upload-area {
        padding: 2rem 1rem;
        min-height: 200px;
    }
    
    .upload-icon {
        font-size: 3rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .progress-section {
        padding: 1.5rem;
    }
    
    .stat-item {
        margin-bottom: 1rem;
    }
    
    .result-stat h4 {
        font-size: 2rem;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--success-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color) 0%, var(--success-color) 100%) 1;
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}
