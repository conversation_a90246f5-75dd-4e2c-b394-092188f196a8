/* Components Styles for Excel to HTML AI Converter */

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    background: var(--light-color);
}

.modal-title {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    color: var(--dark-color);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    background: var(--light-color);
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.alert-success {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
    border-right: 4px solid var(--success-color);
}

.alert-warning {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
    border-right: 4px solid var(--warning-color);
}

.alert-danger {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
    border-right: 4px solid var(--danger-color);
}

.alert-info {
    background: rgba(8, 145, 178, 0.1);
    color: var(--info-color);
    border-right: 4px solid var(--info-color);
}

/* Toast Styles */
.toast-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 9999;
}

.toast {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1rem;
    min-width: 300px;
}

.toast-header {
    background: var(--light-color);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-weight: 600;
}

.toast-body {
    padding: 1rem;
}

/* Dropdown Styles */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    font-weight: 500;
}

.dropdown-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.dropdown-item:active {
    background: var(--primary-color);
    color: white;
}

/* Badge Styles */
.badge {
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
    font-weight: 600;
    font-size: 0.8rem;
}

.badge-primary {
    background: var(--primary-color);
}

.badge-success {
    background: var(--success-color);
}

.badge-warning {
    background: var(--warning-color);
}

.badge-danger {
    background: var(--danger-color);
}

.badge-info {
    background: var(--info-color);
}

/* Spinner Styles */
.spinner-custom {
    width: 2rem;
    height: 2rem;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spinner-lg {
    width: 3rem;
    height: 3rem;
    border-width: 4px;
}

.spinner-sm {
    width: 1rem;
    height: 1rem;
    border-width: 2px;
}

/* File Preview Styles */
.file-preview {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-top: 1rem;
}

.file-preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.file-info {
    display: flex;
    align-items: center;
}

.file-icon {
    font-size: 2rem;
    color: var(--success-color);
    margin-left: 1rem;
}

.file-details h6 {
    margin-bottom: 0.25rem;
    font-weight: 600;
    color: var(--dark-color);
}

.file-details p {
    margin-bottom: 0;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.file-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.file-content {
    max-height: 300px;
    overflow-y: auto;
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* API Status Indicator */
.api-status {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0.25rem;
}

.api-status.active {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.api-status.inactive {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.api-status.limited {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.api-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 0.5rem;
}

.api-status.active .api-status-indicator {
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.api-status.inactive .api-status-indicator {
    background: var(--danger-color);
}

.api-status.limited .api-status-indicator {
    background: var(--warning-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Settings Panel */
.settings-panel {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.settings-group {
    margin-bottom: 2rem;
}

.settings-group:last-child {
    margin-bottom: 0;
}

.settings-group h6 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.settings-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.settings-item:last-child {
    border-bottom: none;
}

.settings-label {
    flex: 1;
    margin-left: 1rem;
}

.settings-label h6 {
    margin-bottom: 0.25rem;
    font-weight: 600;
    color: var(--dark-color);
    border: none;
    padding: 0;
}

.settings-label p {
    margin-bottom: 0;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.settings-control {
    flex-shrink: 0;
}

/* Switch Toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: var(--transition);
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Range Slider */
.range-slider {
    width: 100%;
    margin: 1rem 0;
}

.range-slider input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.1);
    outline: none;
    -webkit-appearance: none;
}

.range-slider input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--box-shadow);
}

.range-slider input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--box-shadow);
}

.range-value {
    text-align: center;
    font-weight: 600;
    color: var(--primary-color);
    margin-top: 0.5rem;
}

/* Tabs */
.nav-tabs {
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 1rem 1.5rem;
    transition: var(--transition);
}

.nav-tabs .nav-link:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.nav-tabs .nav-link.active {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: none;
}

/* Accordion */
.accordion-item {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    overflow: hidden;
}

.accordion-header {
    margin-bottom: 0;
}

.accordion-button {
    background: var(--light-color);
    border: none;
    padding: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    transition: var(--transition);
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-body {
    padding: 1.5rem;
    background: white;
}

/* Pagination */
.pagination {
    justify-content: center;
    margin-top: 2rem;
}

.page-link {
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: var(--dark-color);
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.page-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

/* Breadcrumb */
.breadcrumb {
    background: var(--light-color);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--secondary-color);
}
