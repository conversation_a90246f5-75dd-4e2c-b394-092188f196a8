<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة تحويل Excel إلى HTML بالذكاء الصناعي - محول احترافي</title>
    <meta name="description" content="أداة احترافية تستخدم الذكاء الصناعي لتحويل محتوى خلايا Excel إلى صفحات HTML منسقة بجودة عالية مع دعم الملفات الكبيرة والتصدير المتعدد">
    <meta name="keywords" content="تحويل Excel إلى HTML, ذكاء صناعي, تحويل الملفات, HTML منسق, أداة تحويل">
    <meta name="author" content="Excel to HTML AI Converter">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="أداة تحويل Excel إلى HTML بالذكاء الصناعي">
    <meta property="og:description" content="محول احترافي يستخدم الذكاء الصناعي لتحويل خلايا Excel إلى صفحات HTML منسقة">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="ar_AR">
    
    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "أداة تحويل Excel إلى HTML بالذكاء الصناعي",
        "description": "أداة احترافية تستخدم الذكاء الصناعي لتحويل محتوى خلايا Excel إلى صفحات HTML منسقة",
        "applicationCategory": "ProductivityApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "author": {
            "@type": "Organization",
            "name": "Excel to HTML AI Converter"
        }
    }
    </script>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo & Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body>
    <!-- Header -->
    <header class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="header-title">
                        <i class="fas fa-file-excel text-success me-3"></i>
                        أداة تحويل Excel إلى HTML بالذكاء الصناعي
                    </h1>
                    <p class="header-subtitle">محول احترافي يستخدم الذكاء الصناعي لتحويل خلايا Excel إلى صفحات HTML منسقة</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="header-actions">
                        <button class="btn btn-outline-primary me-2" id="settingsBtn">
                            <i class="fas fa-cog"></i> الإعدادات
                        </button>
                        <button class="btn btn-outline-info" id="helpBtn">
                            <i class="fas fa-question-circle"></i> المساعدة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Upload Section -->
            <section class="upload-section mb-5">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-upload me-2"></i>
                            رفع الملف
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                        <h4>اسحب وأفلت ملف Excel هنا</h4>
                                        <p>أو انقر للاختيار</p>
                                        <input type="file" id="fileInput" accept=".xlsx,.xls" hidden>
                                        <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                            <i class="fas fa-folder-open me-2"></i>
                                            اختيار ملف
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="manual-input-area">
                                    <h5><i class="fas fa-edit me-2"></i>أو أدخل النص يدوياً</h5>
                                    <textarea class="form-control" id="manualInput" rows="8" 
                                              placeholder="أدخل النص هنا... كل سطر سيمثل خلية منفصلة"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Configuration Section -->
            <section class="config-section mb-5">
                <div class="card shadow-lg">
                    <div class="card-header bg-info text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            إعدادات التحويل
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="customPrompt" class="form-label">
                                        <i class="fas fa-magic me-2"></i>
                                        تعليمات إضافية للذكاء الصناعي (اختياري)
                                    </label>
                                    <textarea class="form-control" id="customPrompt" rows="4" 
                                              placeholder="أدخل تعليمات إضافية للذكاء الصناعي للالتزام بها أثناء التحويل..."></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-palette me-2"></i>
                                        نمط التصميم
                                    </label>
                                    <select class="form-select" id="designStyle">
                                        <option value="modern">حديث</option>
                                        <option value="classic">كلاسيكي</option>
                                        <option value="minimal">بسيط</option>
                                        <option value="professional">احترافي</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-language me-2"></i>
                                        لغة المحتوى
                                    </label>
                                    <select class="form-select" id="contentLanguage">
                                        <option value="ar">العربية</option>
                                        <option value="en">الإنجليزية</option>
                                        <option value="auto">تلقائي</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Process Section -->
            <section class="process-section mb-5">
                <div class="card shadow-lg">
                    <div class="card-header bg-success text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-play me-2"></i>
                            بدء التحويل
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <button class="btn btn-success btn-lg" id="startProcessBtn" disabled>
                                <i class="fas fa-rocket me-2"></i>
                                بدء عملية التحويل
                            </button>
                        </div>
                        
                        <!-- Progress Section -->
                        <div class="progress-section" id="progressSection" style="display: none;">
                            <div class="progress-info mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5 id="progressTitle">جاري التحضير...</h5>
                                        <p id="progressDescription">يتم تحضير الملف للمعالجة</p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <div class="progress-controls">
                                            <button class="btn btn-warning btn-sm" id="pauseBtn">
                                                <i class="fas fa-pause"></i> إيقاف مؤقت
                                            </button>
                                            <button class="btn btn-danger btn-sm" id="stopBtn">
                                                <i class="fas fa-stop"></i> إيقاف
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     id="progressBar" role="progressbar" style="width: 0%">
                                    <span id="progressPercent">0%</span>
                                </div>
                            </div>
                            
                            <div class="progress-details">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="stat-item">
                                            <h6>المعالج</h6>
                                            <span id="processedCount">0</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-item">
                                            <h6>المتبقي</h6>
                                            <span id="remainingCount">0</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-item">
                                            <h6>الوقت المتبقي</h6>
                                            <span id="estimatedTime">--:--</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-item">
                                            <h6>السرعة</h6>
                                            <span id="processingSpeed">-- عنصر/دقيقة</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="card shadow-lg">
                    <div class="card-header bg-warning text-dark">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            النتائج والتصدير
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="results-summary mb-4">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <div class="result-stat">
                                        <h4 id="totalProcessed">0</h4>
                                        <p>صفحة تم إنشاؤها</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="result-stat">
                                        <h4 id="totalTime">00:00</h4>
                                        <p>إجمالي الوقت</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="result-stat">
                                        <h4 id="successRate">100%</h4>
                                        <p>معدل النجاح</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="export-options">
                            <h5 class="mb-3">
                                <i class="fas fa-download me-2"></i>
                                خيارات التصدير
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <button class="btn btn-primary w-100 mb-2" id="exportHtmlBtn">
                                        <i class="fas fa-code me-2"></i>
                                        تصدير HTML منسق
                                    </button>
                                    <button class="btn btn-success w-100 mb-2" id="exportExcelBtn">
                                        <i class="fas fa-file-excel me-2"></i>
                                        تصدير Excel
                                    </button>
                                    <button class="btn btn-info w-100 mb-2" id="exportTxtBtn">
                                        <i class="fas fa-file-alt me-2"></i>
                                        تصدير نص
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-danger w-100 mb-2" id="exportPdfBtn">
                                        <i class="fas fa-file-pdf me-2"></i>
                                        تصدير PDF
                                    </button>
                                    <button class="btn btn-warning w-100 mb-2" id="exportZipBtn">
                                        <i class="fas fa-file-archive me-2"></i>
                                        تصدير مضغوط
                                    </button>
                                    <button class="btn btn-secondary w-100 mb-2" id="exportSettingsBtn">
                                        <i class="fas fa-cog me-2"></i>
                                        تصدير الإعدادات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 أداة تحويل Excel إلى HTML بالذكاء الصناعي. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-end">
                    <div class="footer-links">
                        <a href="#" class="me-3">الشروط والأحكام</a>
                        <a href="#" class="me-3">سياسة الخصوصية</a>
                        <a href="#">اتصل بنا</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Modals will be loaded here -->
    <div id="modalsContainer"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    
    <!-- Custom Scripts -->
    <script src="js/config.js"></script>
    <script src="js/api-manager.js"></script>
    <script src="js/excel-processor.js"></script>
    <script src="js/ai-processor.js"></script>
    <script src="js/progress-manager.js"></script>
    <script src="js/export-manager.js"></script>
    <script src="js/settings-manager.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
