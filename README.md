# أداة تحويل Excel إلى HTML بالذكاء الصناعي

أداة احترافية تستخدم الذكاء الصناعي لتحويل محتوى خلايا Excel إلى صفحات HTML منسقة بجودة عالية.

## الميزات الرئيسية

### 🚀 معالجة ذكية
- تحويل خلايا Excel إلى صفحات HTML منسقة احترافياً
- دعم المحتوى العربي مع اتجاه RTL
- معالجة الملفات الكبيرة مع تقسيم ذكي
- إدارة متقدمة لحدود API والتوكنز

### 🎨 تصميم احترافي
- أربعة أنماط تصميم: حديث، كلاسيكي، بسيط، احترافي
- دعم خطوط Cairo و Tajawal
- تصميم متجاوب (Responsive)
- تأثيرات بصرية وحركات سلسة

### ⚡ أداء متقدم
- معالجة متوازية لتسريع العملية
- تدوير ذكي لمفاتيح Gemini API
- نظام إعادة المحاولة التلقائي
- شريط تقدم تفاعلي مع إحصائيات مفصلة

### 📤 تصدير متعدد الصيغ
- HTML منسق مع محرك بحث
- ملف Excel احترافي
- PDF مع دعم العربية
- ملف نصي
- ملف مضغوط شامل

## متطلبات النظام

- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- اتصال بالإنترنت للوصول إلى Gemini API
- مفاتيح Gemini API صالحة

## التثبيت والاستخدام

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd excel-to-html-ai-converter
```

### 2. فتح التطبيق
افتح ملف `index.html` في متصفح الويب

### 3. الاستخدام
1. **رفع الملف**: اختر ملف Excel أو أدخل النص يدوياً
2. **الإعدادات**: اختر نمط التصميم ولغة المحتوى
3. **التعليمات الإضافية**: أضف تعليمات خاصة (اختياري)
4. **بدء المعالجة**: انقر على زر "بدء عملية التحويل"
5. **التصدير**: اختر صيغة التصدير المناسبة

## هيكل المشروع

```
excel-to-html-ai-converter/
├── index.html              # الصفحة الرئيسية
├── css/                    # ملفات التنسيق
│   ├── main.css           # التنسيق الرئيسي
│   ├── components.css     # تنسيق المكونات
│   └── animations.css     # الحركات والتأثيرات
├── js/                     # ملفات JavaScript
│   ├── config.js          # الإعدادات والثوابت
│   ├── api-manager.js     # إدارة مفاتيح API
│   ├── excel-processor.js # معالج ملفات Excel
│   ├── ai-processor.js    # معالج الذكاء الصناعي
│   ├── progress-manager.js # مدير التقدم
│   ├── export-manager.js  # مدير التصدير
│   ├── settings-manager.js # مدير الإعدادات
│   └── main.js            # الملف الرئيسي
└── README.md              # هذا الملف
```

## إعداد مفاتيح API

يتم تضمين مفاتيح Gemini API في ملف `js/config.js`. يمكنك إضافة مفاتيح إضافية أو تعديل الموجودة:

```javascript
GEMINI_API: {
    KEYS: [
        'your-api-key-1',
        'your-api-key-2',
        // ... المزيد من المفاتيح
    ]
}
```

## الإعدادات المتقدمة

### إعدادات المعالجة
- **حجم القطعة**: حجم النص في كل قطعة (1000-8000 حرف)
- **الطلبات المتزامنة**: عدد الطلبات المتزامنة (1-10)
- **إعادة المحاولة**: تفعيل إعادة المحاولة للطلبات الفاشلة

### إعدادات التصدير
- **تضمين البيانات الوصفية**: إضافة معلومات إضافية
- **الحفاظ على التنسيق**: الحفاظ على HTML الأصلي
- **صيغة التصدير الافتراضية**: الصيغة المفضلة

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. خطأ في تحميل الملف**
- تأكد من أن الملف بصيغة Excel (.xlsx أو .xls)
- تحقق من حجم الملف (الحد الأقصى 50 ميجابايت)

**2. فشل في المعالجة**
- تحقق من اتصال الإنترنت
- تأكد من صحة مفاتيح API
- راجع حدود الاستخدام اليومي

**3. بطء في المعالجة**
- قلل عدد الطلبات المتزامنة
- قسم الملفات الكبيرة إلى أجزاء أصغر
- تحقق من حالة مفاتيح API

## الأمان والخصوصية

- جميع المعالجات تتم في المتصفح
- لا يتم حفظ البيانات على خوادم خارجية
- مفاتيح API محمية ومشفرة
- إمكانية مسح البيانات المحلية

## المساهمة في التطوير

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

- افتح Issue في GitHub
- راسلنا على البريد الإلكتروني
- راجع دليل الاستخدام في التطبيق

## التحديثات المستقبلية

### الإصدار القادم (v1.1)
- [ ] دعم المزيد من صيغ الملفات
- [ ] تحسينات في الأداء
- [ ] ميزات تصدير إضافية
- [ ] واجهة مستخدم محسنة

### الإصدار المتقدم (v2.0)
- [ ] دعم قواعد البيانات
- [ ] API للمطورين
- [ ] تطبيق سطح المكتب
- [ ] المزيد من نماذج الذكاء الصناعي

## الشكر والتقدير

شكر خاص لـ:
- Google Gemini API
- Bootstrap Framework
- Font Awesome Icons
- SheetJS Library
- jsPDF Library

---

**تم تطوير هذه الأداة بعناية لتوفير أفضل تجربة لتحويل Excel إلى HTML باستخدام الذكاء الصناعي.**
