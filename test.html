<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أداة تحويل Excel إلى HTML</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #2563eb;
            margin-bottom: 15px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
        .sample-data {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار أداة تحويل Excel إلى HTML بالذكاء الصناعي</h1>
        <p>هذه الصفحة تحتوي على اختبارات للتأكد من عمل جميع مكونات التطبيق بشكل صحيح.</p>
        
        <div class="test-section">
            <h3>1. اختبار تحميل المكونات</h3>
            <button onclick="testComponentsLoading()">تشغيل الاختبار</button>
            <div id="components-test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>2. اختبار معالج Excel</h3>
            <button onclick="testExcelProcessor()">اختبار النص اليدوي</button>
            <div id="excel-test-results"></div>
            <div class="sample-data">بيانات اختبار:
مقال عن الذكاء الصناعي
تطبيقات الذكاء الصناعي في التعليم
&lt;h2&gt;عنوان مع HTML&lt;/h2&gt;&lt;p&gt;فقرة مع تنسيق&lt;/p&gt;
نص طويل يحتوي على معلومات مفصلة حول موضوع معين ويتطلب معالجة خاصة من الذكاء الصناعي</div>
        </div>
        
        <div class="test-section">
            <h3>3. اختبار مدير API</h3>
            <button onclick="testAPIManager()">اختبار حالة API</button>
            <div id="api-test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>4. اختبار الإعدادات</h3>
            <button onclick="testSettingsManager()">اختبار حفظ/تحميل الإعدادات</button>
            <div id="settings-test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>5. اختبار التصدير</h3>
            <button onclick="testExportManager()">اختبار وظائف التصدير</button>
            <div id="export-test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>6. اختبار شامل</h3>
            <button onclick="runFullTest()">تشغيل جميع الاختبارات</button>
            <div id="full-test-results"></div>
        </div>
    </div>

    <!-- تحميل المكتبات المطلوبة -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    
    <!-- تحميل ملفات التطبيق -->
    <script src="js/config.js"></script>
    <script src="js/api-manager.js"></script>
    <script src="js/excel-processor.js"></script>
    <script src="js/ai-processor.js"></script>
    <script src="js/progress-manager.js"></script>
    <script src="js/export-manager.js"></script>
    <script src="js/settings-manager.js"></script>

    <script>
        // وظائف الاختبار
        function addTestResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result test-${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearTestResults(containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
        }

        // اختبار تحميل المكونات
        function testComponentsLoading() {
            clearTestResults('components-test-results');
            
            const components = [
                { name: 'CONFIG', obj: window.CONFIG },
                { name: 'UTILS', obj: window.UTILS },
                { name: 'apiManager', obj: window.apiManager },
                { name: 'excelProcessor', obj: window.excelProcessor },
                { name: 'aiProcessor', obj: window.aiProcessor },
                { name: 'progressManager', obj: window.progressManager },
                { name: 'exportManager', obj: window.exportManager },
                { name: 'settingsManager', obj: window.settingsManager }
            ];

            components.forEach(component => {
                if (component.obj) {
                    addTestResult('components-test-results', `✓ ${component.name} تم تحميله بنجاح`, 'pass');
                } else {
                    addTestResult('components-test-results', `✗ ${component.name} فشل في التحميل`, 'fail');
                }
            });
        }

        // اختبار معالج Excel
        function testExcelProcessor() {
            clearTestResults('excel-test-results');
            
            if (!window.excelProcessor) {
                addTestResult('excel-test-results', '✗ معالج Excel غير متوفر', 'fail');
                return;
            }

            try {
                const testData = `مقال عن الذكاء الصناعي
تطبيقات الذكاء الصناعي في التعليم
<h2>عنوان مع HTML</h2><p>فقرة مع تنسيق</p>
نص طويل يحتوي على معلومات مفصلة حول موضوع معين`;

                const result = window.excelProcessor.processManualInput(testData);
                
                if (result && result.length > 0) {
                    addTestResult('excel-test-results', `✓ تم معالجة ${result.length} عنصر بنجاح`, 'pass');
                    
                    // اختبار خصائص البيانات
                    const firstItem = result[0];
                    if (firstItem.id && firstItem.content && firstItem.wordCount !== undefined) {
                        addTestResult('excel-test-results', '✓ هيكل البيانات صحيح', 'pass');
                    } else {
                        addTestResult('excel-test-results', '✗ هيكل البيانات غير صحيح', 'fail');
                    }
                    
                    // اختبار كشف HTML
                    const htmlItem = result.find(item => item.hasHtml);
                    if (htmlItem) {
                        addTestResult('excel-test-results', '✓ تم كشف محتوى HTML بنجاح', 'pass');
                    } else {
                        addTestResult('excel-test-results', '✗ فشل في كشف محتوى HTML', 'fail');
                    }
                    
                } else {
                    addTestResult('excel-test-results', '✗ فشل في معالجة البيانات', 'fail');
                }
                
            } catch (error) {
                addTestResult('excel-test-results', `✗ خطأ في المعالجة: ${error.message}`, 'fail');
            }
        }

        // اختبار مدير API
        function testAPIManager() {
            clearTestResults('api-test-results');
            
            if (!window.apiManager) {
                addTestResult('api-test-results', '✗ مدير API غير متوفر', 'fail');
                return;
            }

            try {
                // اختبار حالة API
                const status = window.apiManager.getAPIStatus();
                if (status && typeof status === 'object') {
                    addTestResult('api-test-results', `✓ حالة API: ${status.active} نشط، ${status.blocked} محظور`, 'pass');
                    
                    if (status.total > 0) {
                        addTestResult('api-test-results', `✓ تم تحميل ${status.total} مفتاح API`, 'pass');
                    } else {
                        addTestResult('api-test-results', '✗ لا توجد مفاتيح API', 'fail');
                    }
                } else {
                    addTestResult('api-test-results', '✗ فشل في الحصول على حالة API', 'fail');
                }

                // اختبار تفاصيل المفاتيح
                const keyDetails = window.apiManager.getKeyDetails();
                if (keyDetails && keyDetails.length > 0) {
                    addTestResult('api-test-results', `✓ تفاصيل ${keyDetails.length} مفتاح متوفرة`, 'pass');
                } else {
                    addTestResult('api-test-results', '✗ فشل في الحصول على تفاصيل المفاتيح', 'fail');
                }

            } catch (error) {
                addTestResult('api-test-results', `✗ خطأ في اختبار API: ${error.message}`, 'fail');
            }
        }

        // اختبار مدير الإعدادات
        function testSettingsManager() {
            clearTestResults('settings-test-results');
            
            if (!window.settingsManager) {
                addTestResult('settings-test-results', '✗ مدير الإعدادات غير متوفر', 'fail');
                return;
            }

            try {
                // اختبار الحصول على الإعدادات
                const settings = window.settingsManager.getAllSettings();
                if (settings && typeof settings === 'object') {
                    addTestResult('settings-test-results', '✓ تم تحميل الإعدادات بنجاح', 'pass');
                    
                    // اختبار إعداد محدد
                    const language = window.settingsManager.getSetting('language');
                    if (language) {
                        addTestResult('settings-test-results', `✓ اللغة الحالية: ${language}`, 'pass');
                    }
                    
                    // اختبار تعديل إعداد
                    const originalTheme = window.settingsManager.getSetting('theme');
                    window.settingsManager.setSetting('theme', 'test');
                    const newTheme = window.settingsManager.getSetting('theme');
                    
                    if (newTheme === 'test') {
                        addTestResult('settings-test-results', '✓ تم تعديل الإعداد بنجاح', 'pass');
                        // إعادة الإعداد الأصلي
                        window.settingsManager.setSetting('theme', originalTheme);
                    } else {
                        addTestResult('settings-test-results', '✗ فشل في تعديل الإعداد', 'fail');
                    }
                    
                } else {
                    addTestResult('settings-test-results', '✗ فشل في تحميل الإعدادات', 'fail');
                }

            } catch (error) {
                addTestResult('settings-test-results', `✗ خطأ في اختبار الإعدادات: ${error.message}`, 'fail');
            }
        }

        // اختبار مدير التصدير
        function testExportManager() {
            clearTestResults('export-test-results');
            
            if (!window.exportManager) {
                addTestResult('export-test-results', '✗ مدير التصدير غير متوفر', 'fail');
                return;
            }

            try {
                // إنشاء بيانات اختبار
                const testResults = [
                    {
                        id: 'test1',
                        result: '<html><body><h1>اختبار 1</h1></body></html>',
                        status: 'success',
                        originalItem: {
                            cellReference: 'A1',
                            content: 'محتوى اختبار 1',
                            wordCount: 3,
                            charCount: 15,
                            hasHtml: false
                        }
                    },
                    {
                        id: 'test2',
                        result: '<html><body><h2>اختبار 2</h2></body></html>',
                        status: 'success',
                        originalItem: {
                            cellReference: 'A2',
                            content: 'محتوى اختبار 2',
                            wordCount: 3,
                            charCount: 15,
                            hasHtml: false
                        }
                    }
                ];

                // تعيين النتائج
                window.exportManager.setResults(testResults);
                addTestResult('export-test-results', '✓ تم تعيين بيانات الاختبار', 'pass');

                // اختبار إنشاء HTML
                try {
                    const htmlContent = window.exportManager.generateHTMLExport();
                    if (htmlContent && htmlContent.includes('<!DOCTYPE html>')) {
                        addTestResult('export-test-results', '✓ تم إنشاء HTML بنجاح', 'pass');
                    } else {
                        addTestResult('export-test-results', '✗ فشل في إنشاء HTML', 'fail');
                    }
                } catch (htmlError) {
                    addTestResult('export-test-results', `✗ خطأ في إنشاء HTML: ${htmlError.message}`, 'fail');
                }

                // اختبار الحصول على الإعدادات الحالية
                const currentSettings = window.exportManager.getCurrentSettings();
                if (currentSettings && currentSettings.version) {
                    addTestResult('export-test-results', '✓ تم الحصول على إعدادات التصدير', 'pass');
                } else {
                    addTestResult('export-test-results', '✗ فشل في الحصول على إعدادات التصدير', 'fail');
                }

            } catch (error) {
                addTestResult('export-test-results', `✗ خطأ في اختبار التصدير: ${error.message}`, 'fail');
            }
        }

        // تشغيل جميع الاختبارات
        function runFullTest() {
            clearTestResults('full-test-results');
            addTestResult('full-test-results', 'بدء الاختبار الشامل...', 'info');
            
            setTimeout(() => {
                testComponentsLoading();
                testExcelProcessor();
                testAPIManager();
                testSettingsManager();
                testExportManager();
                
                addTestResult('full-test-results', 'تم إكمال جميع الاختبارات. راجع النتائج أعلاه.', 'info');
            }, 100);
        }

        // تشغيل اختبار أساسي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                addTestResult('full-test-results', 'تم تحميل صفحة الاختبار بنجاح. انقر على الأزرار لتشغيل الاختبارات.', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
