/**
 * Settings Manager for handling application settings
 * Supports save, load, import, export, and reset functionality
 */

class SettingsManager {
    constructor() {
        this.settings = { ...CONFIG.DEFAULT_SETTINGS };
        this.settingsKey = CONFIG.STORAGE.PREFIX + CONFIG.STORAGE.SETTINGS_KEY;
        
        // Load saved settings
        this.loadSettings();
        
        // Initialize UI
        this.initializeUI();
        
        // Bind events
        this.bindEvents();
    }
    
    /**
     * Initialize settings UI
     */
    initializeUI() {
        this.createSettingsModal();
        this.updateUIFromSettings();
    }
    
    /**
     * Create settings modal
     */
    createSettingsModal() {
        const modalHTML = `
            <div class="modal fade" id="settingsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات التطبيق
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="settings-container">
                                ${this.generateSettingsHTML()}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-warning" id="resetSettingsBtn">إعادة تعيين</button>
                            <button type="button" class="btn btn-info" id="importSettingsBtn">استيراد</button>
                            <button type="button" class="btn btn-success" id="saveSettingsBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to page
        const modalsContainer = document.getElementById('modalsContainer') || document.body;
        modalsContainer.insertAdjacentHTML('beforeend', modalHTML);
    }
    
    /**
     * Generate settings HTML
     */
    generateSettingsHTML() {
        return `
            <div class="row">
                <div class="col-md-6">
                    <div class="settings-group">
                        <h6>الإعدادات العامة</h6>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>اللغة</h6>
                                <p>لغة واجهة التطبيق</p>
                            </div>
                            <div class="settings-control">
                                <select class="form-select" id="setting-language">
                                    <option value="ar">العربية</option>
                                    <option value="en">English</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>المظهر</h6>
                                <p>نمط المظهر العام</p>
                            </div>
                            <div class="settings-control">
                                <select class="form-select" id="setting-theme">
                                    <option value="light">فاتح</option>
                                    <option value="dark">داكن</option>
                                    <option value="auto">تلقائي</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>نمط التصميم</h6>
                                <p>نمط تصميم الصفحات المولدة</p>
                            </div>
                            <div class="settings-control">
                                <select class="form-select" id="setting-designStyle">
                                    <option value="modern">حديث</option>
                                    <option value="classic">كلاسيكي</option>
                                    <option value="minimal">بسيط</option>
                                    <option value="professional">احترافي</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>لغة المحتوى</h6>
                                <p>لغة المحتوى المولد</p>
                            </div>
                            <div class="settings-control">
                                <select class="form-select" id="setting-contentLanguage">
                                    <option value="ar">العربية</option>
                                    <option value="en">الإنجليزية</option>
                                    <option value="auto">تلقائي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-group">
                        <h6>إعدادات المعالجة</h6>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>حجم القطعة</h6>
                                <p>حجم النص في كل قطعة (أحرف)</p>
                            </div>
                            <div class="settings-control">
                                <div class="range-slider">
                                    <input type="range" id="setting-chunkSize" min="1000" max="8000" step="500" class="form-range">
                                    <div class="range-value" id="chunkSizeValue">4000</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>الطلبات المتزامنة</h6>
                                <p>عدد الطلبات المتزامنة للذكاء الصناعي</p>
                            </div>
                            <div class="settings-control">
                                <div class="range-slider">
                                    <input type="range" id="setting-maxConcurrentRequests" min="1" max="10" step="1" class="form-range">
                                    <div class="range-value" id="maxConcurrentRequestsValue">3</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>إعادة المحاولة</h6>
                                <p>إعادة محاولة الطلبات الفاشلة</p>
                            </div>
                            <div class="settings-control">
                                <div class="switch">
                                    <input type="checkbox" id="setting-retryFailedRequests">
                                    <span class="slider"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="settings-group">
                        <h6>إعدادات الواجهة</h6>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>الحفظ التلقائي</h6>
                                <p>حفظ التقدم تلقائياً</p>
                            </div>
                            <div class="settings-control">
                                <div class="switch">
                                    <input type="checkbox" id="setting-autoSave">
                                    <span class="slider"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>عرض التقدم</h6>
                                <p>إظهار شريط التقدم التفصيلي</p>
                            </div>
                            <div class="settings-control">
                                <div class="switch">
                                    <input type="checkbox" id="setting-showProgress">
                                    <span class="slider"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>الإشعارات</h6>
                                <p>تفعيل الإشعارات</p>
                            </div>
                            <div class="settings-control">
                                <div class="switch">
                                    <input type="checkbox" id="setting-enableNotifications">
                                    <span class="slider"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-group">
                        <h6>إعدادات التصدير</h6>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>صيغة التصدير الافتراضية</h6>
                                <p>الصيغة المفضلة للتصدير</p>
                            </div>
                            <div class="settings-control">
                                <select class="form-select" id="setting-exportFormat">
                                    <option value="html">HTML</option>
                                    <option value="excel">Excel</option>
                                    <option value="pdf">PDF</option>
                                    <option value="txt">نص</option>
                                    <option value="zip">مضغوط</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>تضمين البيانات الوصفية</h6>
                                <p>إضافة معلومات إضافية للملفات المصدرة</p>
                            </div>
                            <div class="settings-control">
                                <div class="switch">
                                    <input type="checkbox" id="setting-includeMetadata">
                                    <span class="slider"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>الحفاظ على التنسيق</h6>
                                <p>الحفاظ على تنسيق HTML الأصلي</p>
                            </div>
                            <div class="settings-control">
                                <div class="switch">
                                    <input type="checkbox" id="setting-preserveFormatting">
                                    <span class="slider"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-group">
                        <h6>إدارة الإعدادات</h6>
                        
                        <div class="settings-item">
                            <div class="settings-label">
                                <h6>استيراد الإعدادات</h6>
                                <p>استيراد إعدادات من ملف</p>
                            </div>
                            <div class="settings-control">
                                <input type="file" id="importSettingsFile" accept=".json" class="form-control" style="display: none;">
                                <button class="btn btn-outline-primary btn-sm" onclick="document.getElementById('importSettingsFile').click()">
                                    <i class="fas fa-upload me-1"></i> اختيار ملف
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Bind event listeners
     */
    bindEvents() {
        // Settings button
        const settingsBtn = document.getElementById('settingsBtn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showSettingsModal();
            });
        }
        
        // Save settings button
        document.addEventListener('click', (e) => {
            if (e.target.id === 'saveSettingsBtn') {
                this.saveSettingsFromUI();
            } else if (e.target.id === 'resetSettingsBtn') {
                this.resetSettings();
            } else if (e.target.id === 'importSettingsBtn') {
                document.getElementById('importSettingsFile').click();
            }
        });
        
        // Import settings file
        document.addEventListener('change', (e) => {
            if (e.target.id === 'importSettingsFile') {
                this.importSettingsFromFile(e.target.files[0]);
            }
        });
        
        // Range sliders
        document.addEventListener('input', (e) => {
            if (e.target.type === 'range') {
                this.updateRangeValue(e.target);
            }
        });
        
        // Auto-save on change
        document.addEventListener('change', (e) => {
            if (e.target.id.startsWith('setting-') && this.settings.autoSave) {
                this.saveSettingsFromUI();
            }
        });
    }
    
    /**
     * Show settings modal
     */
    showSettingsModal() {
        const modal = new bootstrap.Modal(document.getElementById('settingsModal'));
        this.updateUIFromSettings();
        modal.show();
    }
    
    /**
     * Update UI from current settings
     */
    updateUIFromSettings() {
        Object.keys(this.settings).forEach(key => {
            const element = document.getElementById(`setting-${key}`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = this.settings[key];
                } else if (element.type === 'range') {
                    element.value = this.settings[key];
                    this.updateRangeValue(element);
                } else {
                    element.value = this.settings[key];
                }
            }
        });
    }
    
    /**
     * Update range slider value display
     */
    updateRangeValue(rangeElement) {
        const valueElement = document.getElementById(rangeElement.id.replace('setting-', '') + 'Value');
        if (valueElement) {
            valueElement.textContent = rangeElement.value;
        }
    }
    
    /**
     * Save settings from UI
     */
    saveSettingsFromUI() {
        const newSettings = { ...this.settings };
        
        Object.keys(newSettings).forEach(key => {
            const element = document.getElementById(`setting-${key}`);
            if (element) {
                if (element.type === 'checkbox') {
                    newSettings[key] = element.checked;
                } else if (element.type === 'range') {
                    newSettings[key] = parseInt(element.value);
                } else {
                    newSettings[key] = element.value;
                }
            }
        });
        
        this.updateSettings(newSettings);
        this.showSuccessMessage(CONFIG.SUCCESS.SETTINGS_SAVED);
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
        if (modal) {
            modal.hide();
        }
    }
    
    /**
     * Update settings
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveSettings();
        this.applySettings();
        
        // Dispatch settings update event
        window.dispatchEvent(new CustomEvent('settingsUpdated', { 
            detail: this.settings 
        }));
    }
    
    /**
     * Apply settings to the application
     */
    applySettings() {
        // Apply theme
        this.applyTheme();
        
        // Apply language
        this.applyLanguage();
        
        // Update other components
        if (window.aiProcessor) {
            window.aiProcessor.settings = this.settings;
        }
        
        if (window.exportManager) {
            window.exportManager.exportSettings = {
                ...window.exportManager.exportSettings,
                ...this.settings
            };
        }
    }
    
    /**
     * Apply theme settings
     */
    applyTheme() {
        const body = document.body;
        
        // Remove existing theme classes
        body.classList.remove('theme-light', 'theme-dark');
        
        if (this.settings.theme === 'dark') {
            body.classList.add('theme-dark');
        } else if (this.settings.theme === 'light') {
            body.classList.add('theme-light');
        } else {
            // Auto theme based on system preference
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                body.classList.add('theme-dark');
            } else {
                body.classList.add('theme-light');
            }
        }
    }
    
    /**
     * Apply language settings
     */
    applyLanguage() {
        const html = document.documentElement;
        
        if (this.settings.language === 'en') {
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
        } else {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
        }
    }
    
    /**
     * Reset settings to defaults
     */
    resetSettings() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
            this.settings = { ...CONFIG.DEFAULT_SETTINGS };
            this.saveSettings();
            this.updateUIFromSettings();
            this.applySettings();
            this.showSuccessMessage('تم إعادة تعيين الإعدادات بنجاح');
        }
    }
    
    /**
     * Import settings from file
     */
    async importSettingsFromFile(file) {
        if (!file) return;
        
        try {
            const text = await this.readFileAsText(file);
            const importedData = JSON.parse(text);
            
            if (importedData.settings) {
                // Validate imported settings
                const validatedSettings = this.validateSettings(importedData.settings);
                this.updateSettings(validatedSettings);
                this.showSuccessMessage(CONFIG.SUCCESS.SETTINGS_IMPORTED);
            } else {
                throw new Error('ملف الإعدادات غير صالح');
            }
            
        } catch (error) {
            this.showErrorMessage('فشل في استيراد الإعدادات: ' + error.message);
        }
    }
    
    /**
     * Validate imported settings
     */
    validateSettings(importedSettings) {
        const validatedSettings = { ...CONFIG.DEFAULT_SETTINGS };
        
        Object.keys(validatedSettings).forEach(key => {
            if (importedSettings.hasOwnProperty(key)) {
                validatedSettings[key] = importedSettings[key];
            }
        });
        
        return validatedSettings;
    }
    
    /**
     * Read file as text
     */
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
            reader.readAsText(file);
        });
    }
    
    /**
     * Save settings to localStorage
     */
    saveSettings() {
        try {
            const settingsData = {
                settings: this.settings,
                version: CONFIG.APP_VERSION,
                savedAt: new Date().toISOString()
            };
            
            localStorage.setItem(this.settingsKey, JSON.stringify(settingsData));
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    }
    
    /**
     * Load settings from localStorage
     */
    loadSettings() {
        try {
            const savedData = localStorage.getItem(this.settingsKey);
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                if (parsedData.settings) {
                    this.settings = { ...CONFIG.DEFAULT_SETTINGS, ...parsedData.settings };
                }
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
            this.settings = { ...CONFIG.DEFAULT_SETTINGS };
        }
        
        // Apply loaded settings
        this.applySettings();
    }
    
    /**
     * Get all settings
     */
    getAllSettings() {
        return { ...this.settings };
    }
    
    /**
     * Get specific setting
     */
    getSetting(key) {
        return this.settings[key];
    }
    
    /**
     * Set specific setting
     */
    setSetting(key, value) {
        this.settings[key] = value;
        this.saveSettings();
        this.applySettings();
    }
    
    /**
     * Show success message
     */
    showSuccessMessage(message) {
        // Implementation depends on notification system
        console.log('Settings Success:', message);
    }
    
    /**
     * Show error message
     */
    showErrorMessage(message) {
        // Implementation depends on notification system
        console.error('Settings Error:', message);
    }
}

// Create global instance
window.settingsManager = new SettingsManager();
