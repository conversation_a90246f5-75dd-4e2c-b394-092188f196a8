/**
 * Configuration file for Excel to HTML AI Converter
 * Contains all application settings and constants
 */

// Application Configuration
const CONFIG = {
    // Application Info
    APP_NAME: 'أداة تحويل Excel إلى HTML بالذكاء الصناعي',
    APP_VERSION: '1.0.0',
    APP_AUTHOR: 'Excel to HTML AI Converter',
    
    // API Configuration
    GEMINI_API: {
        BASE_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
        KEYS: [
            'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
            'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
            'AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw',
            'AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak',
            'AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc',
            'AIzaSyBzlVUgjCkG44mm3_8Rt-ZiLskXsxM29SM',
            'AIzaSyDeSqeSAb4QZ6JEf_LbLTrnVOgcGMSb7ds',
            'AIzaSyAu5ToET8qHvDUPzrDOvkr1T99qZXvZORk',
            'AIzaSyAFu5lHg2W5ibpFbm-k3kNlW-rI96JcQOg',
            'AIzaSyBThniUP9oFxyriMOV4-LpvmJtW3u1gZCQ',
            'AIzaSyAns8tVQ-V6sqbh7UPQB7tqYgWAZUIN6Z4',
            'AIzaSyCS32gY-wcVTRIuE2PY_3ppVloGmvFlAqo',
            'AIzaSyCc3Y4hHWuAxKtJx4E0h-spwYUP6Eqzk4k',
            'AIzaSyAmxg1f8A3s1mXxdUav0zeyC52BF-5QBoA',
            'AIzaSyD7C9Ry7EeCXwvrLkymXuUp3FwqbqloFuQ'
        ],
        MAX_TOKENS: 8192,
        MAX_REQUESTS_PER_MINUTE: 60,
        MAX_REQUESTS_PER_DAY: 1500,
        RETRY_DELAY: 1000, // milliseconds
        MAX_RETRIES: 3
    },
    
    // Processing Configuration
    PROCESSING: {
        MAX_CHUNK_SIZE: 4000, // characters per chunk
        OVERLAP_SIZE: 200, // characters overlap between chunks
        BATCH_SIZE: 5, // number of items to process in parallel
        DELAY_BETWEEN_BATCHES: 2000, // milliseconds
        MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
        SUPPORTED_FORMATS: ['.xlsx', '.xls'],
        AUTO_SAVE_INTERVAL: 30000 // 30 seconds
    },
    
    // UI Configuration
    UI: {
        ANIMATION_DURATION: 300,
        TOAST_DURATION: 5000,
        PROGRESS_UPDATE_INTERVAL: 100,
        DEBOUNCE_DELAY: 500,
        MAX_PREVIEW_LINES: 100,
        ITEMS_PER_PAGE: 20
    },
    
    // Export Configuration
    EXPORT: {
        HTML: {
            TEMPLATE: 'modern',
            INCLUDE_CSS: true,
            INCLUDE_JS: true,
            MINIFY: false
        },
        PDF: {
            FORMAT: 'A4',
            ORIENTATION: 'portrait',
            MARGIN: 20,
            FONT_SIZE: 12,
            FONT_FAMILY: 'Cairo'
        },
        EXCEL: {
            SHEET_NAME: 'النتائج',
            INCLUDE_FORMATTING: true,
            AUTO_FIT_COLUMNS: true
        }
    },
    
    // Storage Configuration
    STORAGE: {
        PREFIX: 'excel_to_html_',
        SETTINGS_KEY: 'settings',
        CACHE_KEY: 'cache',
        HISTORY_KEY: 'history',
        MAX_HISTORY_ITEMS: 50,
        CACHE_EXPIRY: 24 * 60 * 60 * 1000 // 24 hours
    },
    
    // Default Settings
    DEFAULT_SETTINGS: {
        language: 'ar',
        theme: 'light',
        designStyle: 'modern',
        contentLanguage: 'ar',
        autoSave: true,
        showProgress: true,
        enableNotifications: true,
        maxConcurrentRequests: 3,
        chunkSize: 4000,
        retryFailedRequests: true,
        exportFormat: 'html',
        includeMetadata: true,
        preserveFormatting: true
    },
    
    // Prompts Configuration
    PROMPTS: {
        BASE_PROMPT: `أنت خبير في تحويل المحتوى إلى HTML منسق واحترافي. مهمتك هي تحويل النص المعطى إلى صفحة HTML كاملة ومنسقة بشكل احترافي.

المتطلبات:
1. إنشاء صفحة HTML كاملة مع DOCTYPE وجميع العناصر المطلوبة
2. استخدام CSS مدمج لتنسيق احترافي وجذاب
3. دعم اللغة العربية مع اتجاه RTL
4. استخدام خطوط Cairo أو Tajawal
5. تطبيق تصميم حديث مع ألوان متناسقة
6. إضافة تأثيرات بصرية مناسبة
7. جعل التصميم متجاوب (responsive)
8. إضافة meta tags مناسبة للسيو
9. الحفاظ على أي تنسيق HTML موجود في النص الأصلي
10. عدم إضافة أي تعليقات أو توضيحات من الذكاء الصناعي

النص المطلوب تحويله:`,
        
        STYLE_PROMPTS: {
            modern: 'استخدم تصميم حديث مع ألوان زاهية وتدرجات لونية وظلال ناعمة',
            classic: 'استخدم تصميم كلاسيكي أنيق مع ألوان هادئة وخطوط تقليدية',
            minimal: 'استخدم تصميم بسيط ونظيف مع مساحات بيضاء كثيرة وألوان محدودة',
            professional: 'استخدم تصميم احترافي مناسب للأعمال مع ألوان رسمية وتنسيق منظم'
        },
        
        LANGUAGE_PROMPTS: {
            ar: 'اجعل اتجاه الصفحة من اليمين لليسار (RTL) واستخدم خطوط عربية مناسبة',
            en: 'اجعل اتجاه الصفحة من اليسار لليمين (LTR) واستخدم خطوط إنجليزية مناسبة',
            auto: 'اكتشف لغة المحتوى تلقائياً واستخدم الاتجاه والخطوط المناسبة'
        }
    },
    
    // Error Messages
    ERRORS: {
        FILE_TOO_LARGE: 'حجم الملف كبير جداً. الحد الأقصى المسموح هو 50 ميجابايت',
        INVALID_FORMAT: 'صيغة الملف غير مدعومة. يرجى استخدام ملفات Excel (.xlsx أو .xls)',
        API_LIMIT_REACHED: 'تم الوصول للحد الأقصى من الطلبات. يرجى المحاولة لاحقاً',
        NETWORK_ERROR: 'خطأ في الاتصال بالإنترنت. يرجى التحقق من الاتصال والمحاولة مرة أخرى',
        PROCESSING_ERROR: 'حدث خطأ أثناء معالجة الملف. يرجى المحاولة مرة أخرى',
        EXPORT_ERROR: 'حدث خطأ أثناء تصدير الملف. يرجى المحاولة مرة أخرى',
        INVALID_INPUT: 'البيانات المدخلة غير صحيحة. يرجى التحقق والمحاولة مرة أخرى',
        QUOTA_EXCEEDED: 'تم تجاوز الحد المسموح من الاستخدام اليومي'
    },
    
    // Success Messages
    SUCCESS: {
        FILE_UPLOADED: 'تم رفع الملف بنجاح',
        PROCESSING_COMPLETE: 'تم إكمال عملية التحويل بنجاح',
        EXPORT_COMPLETE: 'تم تصدير الملف بنجاح',
        SETTINGS_SAVED: 'تم حفظ الإعدادات بنجاح',
        SETTINGS_IMPORTED: 'تم استيراد الإعدادات بنجاح'
    },
    
    // HTML Templates
    TEMPLATES: {
        HTML_WRAPPER: `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{TITLE}}</title>
    <meta name="description" content="{{DESCRIPTION}}">
    <style>
        {{CSS}}
    </style>
</head>
<body>
    {{CONTENT}}
</body>
</html>`,
        
        SEARCH_TEMPLATE: `
<div class="search-container">
    <input type="text" id="searchInput" placeholder="البحث في المحتوى..." class="search-input">
    <div class="search-results" id="searchResults"></div>
</div>
<script>
    // Search functionality will be added here
</script>`
    },
    
    // Validation Rules
    VALIDATION: {
        MIN_TEXT_LENGTH: 10,
        MAX_TEXT_LENGTH: 1000000,
        MIN_CELLS_COUNT: 1,
        MAX_CELLS_COUNT: 10000,
        ALLOWED_HTML_TAGS: ['p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'table', 'tr', 'td', 'th', 'strong', 'em', 'a', 'img', 'br'],
        BLOCKED_SCRIPTS: ['script', 'iframe', 'object', 'embed', 'form']
    }
};

// Utility Functions
const UTILS = {
    // Generate unique ID
    generateId: () => {
        return 'id_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    },
    
    // Format file size
    formatFileSize: (bytes) => {
        if (bytes === 0) return '0 بايت';
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // Format time duration
    formatDuration: (milliseconds) => {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
        }
    },
    
    // Sanitize HTML
    sanitizeHtml: (html) => {
        const div = document.createElement('div');
        div.innerHTML = html;
        
        // Remove blocked scripts
        CONFIG.VALIDATION.BLOCKED_SCRIPTS.forEach(tag => {
            const elements = div.querySelectorAll(tag);
            elements.forEach(el => el.remove());
        });
        
        return div.innerHTML;
    },
    
    // Validate text length
    validateTextLength: (text) => {
        return text.length >= CONFIG.VALIDATION.MIN_TEXT_LENGTH && 
               text.length <= CONFIG.VALIDATION.MAX_TEXT_LENGTH;
    },
    
    // Get current timestamp
    getCurrentTimestamp: () => {
        return new Date().toISOString();
    },
    
    // Deep clone object
    deepClone: (obj) => {
        return JSON.parse(JSON.stringify(obj));
    },
    
    // Debounce function
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Throttle function
    throttle: (func, limit) => {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// Export configuration for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, UTILS };
}
