/**
 * Main Application Controller
 * Coordinates all components and handles user interactions
 */

class ExcelToHTMLApp {
    constructor() {
        this.isInitialized = false;
        this.currentData = null;
        this.processingResults = null;
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initialize());
        } else {
            this.initialize();
        }
    }
    
    /**
     * Initialize the application
     */
    async initialize() {
        try {
            console.log('Initializing Excel to HTML AI Converter...');
            
            // Initialize components
            await this.initializeComponents();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Setup drag and drop
            this.setupDragAndDrop();
            
            // Initialize UI state
            this.initializeUIState();
            
            // Show welcome message
            this.showWelcomeMessage();
            
            this.isInitialized = true;
            console.log('Application initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize application:', error);
            this.showErrorNotification('فشل في تهيئة التطبيق: ' + error.message);
        }
    }
    
    /**
     * Initialize all components
     */
    async initializeComponents() {
        // Components are already initialized as global instances
        // Just verify they exist
        if (!window.apiManager) {
            throw new Error('API Manager not initialized');
        }
        
        if (!window.excelProcessor) {
            throw new Error('Excel Processor not initialized');
        }
        
        if (!window.aiProcessor) {
            throw new Error('AI Processor not initialized');
        }
        
        if (!window.progressManager) {
            throw new Error('Progress Manager not initialized');
        }
        
        if (!window.exportManager) {
            throw new Error('Export Manager not initialized');
        }
        
        if (!window.settingsManager) {
            throw new Error('Settings Manager not initialized');
        }
        
        console.log('All components verified');
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // File input
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }
        
        // Manual input
        const manualInput = document.getElementById('manualInput');
        if (manualInput) {
            manualInput.addEventListener('input', UTILS.debounce(() => this.handleManualInput(), 500));
        }
        
        // Start processing button
        const startProcessBtn = document.getElementById('startProcessBtn');
        if (startProcessBtn) {
            startProcessBtn.addEventListener('click', () => this.startProcessing());
        }
        
        // Help button
        const helpBtn = document.getElementById('helpBtn');
        if (helpBtn) {
            helpBtn.addEventListener('click', () => this.showHelpModal());
        }
        
        // Listen for component events
        this.setupComponentEventListeners();
    }
    
    /**
     * Setup component event listeners
     */
    setupComponentEventListeners() {
        // API status updates
        window.addEventListener('apiStatusUpdate', (e) => {
            this.updateAPIStatusDisplay(e.detail);
        });
        
        // Settings updates
        window.addEventListener('settingsUpdated', (e) => {
            this.handleSettingsUpdate(e.detail);
        });
        
        // Processing events
        window.addEventListener('processingStarted', (e) => {
            this.handleProcessingStarted(e.detail);
        });
        
        window.addEventListener('processingCompleted', (e) => {
            this.handleProcessingCompleted(e.detail);
        });
        
        window.addEventListener('processingError', (e) => {
            this.handleProcessingError(e.detail);
        });
    }
    
    /**
     * Setup drag and drop functionality
     */
    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea) return;
        
        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });
        
        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.add('dragover');
            }, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.remove('dragover');
            }, false);
        });
        
        // Handle dropped files
        uploadArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelect({ target: { files } });
            }
        }, false);
        
        // Handle click to select file
        uploadArea.addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
    }
    
    /**
     * Prevent default drag behaviors
     */
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    /**
     * Initialize UI state
     */
    initializeUIState() {
        // Hide progress and results sections initially
        const progressSection = document.getElementById('progressSection');
        const resultsSection = document.getElementById('resultsSection');
        
        if (progressSection) {
            progressSection.style.display = 'none';
        }
        
        if (resultsSection) {
            resultsSection.style.display = 'none';
        }
        
        // Disable start button initially
        const startProcessBtn = document.getElementById('startProcessBtn');
        if (startProcessBtn) {
            startProcessBtn.disabled = true;
        }
        
        // Add stagger animations to main sections
        const sections = document.querySelectorAll('.upload-section, .config-section, .process-section');
        sections.forEach((section, index) => {
            section.classList.add('stagger-item');
            section.style.animationDelay = `${index * 0.2}s`;
        });
    }
    
    /**
     * Handle file selection
     */
    async handleFileSelect(event) {
        const files = event.target.files;
        if (!files || files.length === 0) return;
        
        const file = files[0];
        
        try {
            this.showLoadingNotification('جاري تحميل الملف...');
            
            // Process the file
            this.currentData = await window.excelProcessor.processFile(file);
            
            this.showSuccessNotification(`تم تحميل الملف بنجاح. تم العثور على ${this.currentData.length} عنصر.`);
            
            // Clear manual input if file is loaded
            const manualInput = document.getElementById('manualInput');
            if (manualInput) {
                manualInput.value = '';
            }
            
        } catch (error) {
            this.showErrorNotification('فشل في تحميل الملف: ' + error.message);
        }
    }
    
    /**
     * Handle manual input
     */
    handleManualInput() {
        const manualInput = document.getElementById('manualInput');
        if (!manualInput) return;
        
        const text = manualInput.value.trim();
        
        if (text.length === 0) {
            this.currentData = null;
            window.excelProcessor.clearFile();
            return;
        }
        
        try {
            this.currentData = window.excelProcessor.processManualInput(text);
            
            // Clear file input if manual input is used
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.value = '';
            }
            
        } catch (error) {
            this.showErrorNotification('خطأ في معالجة النص: ' + error.message);
        }
    }
    
    /**
     * Start processing
     */
    async startProcessing() {
        try {
            if (!this.currentData || this.currentData.length === 0) {
                throw new Error('يرجى تحميل ملف أو إدخال نص للمعالجة');
            }
            
            // Get custom prompt
            const customPrompt = document.getElementById('customPrompt')?.value || '';
            
            // Get settings
            const settings = {
                designStyle: document.getElementById('designStyle')?.value || 'modern',
                contentLanguage: document.getElementById('contentLanguage')?.value || 'ar'
            };
            
            // Dispatch processing started event
            window.dispatchEvent(new CustomEvent('processingStarted', {
                detail: { totalCount: this.currentData.length }
            }));
            
            // Start AI processing
            this.processingResults = await window.aiProcessor.startProcessing(
                this.currentData,
                customPrompt,
                settings
            );
            
            // Set results for export
            window.exportManager.setResults(this.processingResults.results);
            
            // Dispatch processing completed event
            window.dispatchEvent(new CustomEvent('processingCompleted', {
                detail: this.processingResults
            }));
            
        } catch (error) {
            // Dispatch processing error event
            window.dispatchEvent(new CustomEvent('processingError', {
                detail: { error: error.message }
            }));
        }
    }
    
    /**
     * Handle processing started
     */
    handleProcessingStarted(detail) {
        console.log('Processing started:', detail);
        this.showInfoNotification('بدأت عملية المعالجة...');
    }
    
    /**
     * Handle processing completed
     */
    handleProcessingCompleted(detail) {
        console.log('Processing completed:', detail);
        
        const message = `تم إكمال المعالجة بنجاح! تم إنشاء ${detail.successful} صفحة من أصل ${detail.total}.`;
        this.showSuccessNotification(message);
        
        // Show results section
        const resultsSection = document.getElementById('resultsSection');
        if (resultsSection) {
            resultsSection.style.display = 'block';
            resultsSection.classList.add('animate-fadeIn');
        }
    }
    
    /**
     * Handle processing error
     */
    handleProcessingError(detail) {
        console.error('Processing error:', detail);
        this.showErrorNotification('حدث خطأ في المعالجة: ' + detail.error);
    }
    
    /**
     * Handle settings update
     */
    handleSettingsUpdate(settings) {
        console.log('Settings updated:', settings);
        
        // Update UI elements based on new settings
        this.applySettingsToUI(settings);
    }
    
    /**
     * Apply settings to UI
     */
    applySettingsToUI(settings) {
        // Update design style selector
        const designStyleSelect = document.getElementById('designStyle');
        if (designStyleSelect && settings.designStyle) {
            designStyleSelect.value = settings.designStyle;
        }
        
        // Update content language selector
        const contentLanguageSelect = document.getElementById('contentLanguage');
        if (contentLanguageSelect && settings.contentLanguage) {
            contentLanguageSelect.value = settings.contentLanguage;
        }
    }
    
    /**
     * Update API status display
     */
    updateAPIStatusDisplay(status) {
        // Create or update API status container
        let statusContainer = document.getElementById('apiStatusContainer');
        if (!statusContainer) {
            statusContainer = document.createElement('div');
            statusContainer.id = 'apiStatusContainer';
            statusContainer.className = 'api-status-container mt-3';
            
            const configSection = document.querySelector('.config-section .card-body');
            if (configSection) {
                configSection.appendChild(statusContainer);
            }
        }
        
        // Update status display
        if (window.apiManager) {
            statusContainer.innerHTML = window.apiManager.generateStatusHTML(status);
        }
    }
    
    /**
     * Show help modal
     */
    showHelpModal() {
        // Create help modal if it doesn't exist
        let helpModal = document.getElementById('helpModal');
        if (!helpModal) {
            this.createHelpModal();
            helpModal = document.getElementById('helpModal');
        }
        
        const modal = new bootstrap.Modal(helpModal);
        modal.show();
    }
    
    /**
     * Create help modal
     */
    createHelpModal() {
        const helpHTML = `
            <div class="modal fade" id="helpModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-question-circle me-2"></i>
                                دليل الاستخدام
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="help-content">
                                <h6>كيفية استخدام الأداة:</h6>
                                <ol>
                                    <li><strong>رفع الملف:</strong> اختر ملف Excel (.xlsx أو .xls) أو أدخل النص يدوياً</li>
                                    <li><strong>الإعدادات:</strong> اختر نمط التصميم ولغة المحتوى</li>
                                    <li><strong>التعليمات الإضافية:</strong> أضف تعليمات خاصة للذكاء الصناعي (اختياري)</li>
                                    <li><strong>بدء المعالجة:</strong> انقر على زر "بدء عملية التحويل"</li>
                                    <li><strong>متابعة التقدم:</strong> راقب شريط التقدم والإحصائيات</li>
                                    <li><strong>التصدير:</strong> اختر صيغة التصدير المناسبة</li>
                                </ol>
                                
                                <h6 class="mt-4">الميزات المتقدمة:</h6>
                                <ul>
                                    <li>دعم الملفات الكبيرة مع تقسيم ذكي</li>
                                    <li>معالجة متوازية لتسريع العملية</li>
                                    <li>إدارة ذكية لمفاتيح API</li>
                                    <li>تصدير متعدد الصيغ</li>
                                    <li>محرك بحث في النتائج</li>
                                </ul>
                                
                                <h6 class="mt-4">نصائح للحصول على أفضل النتائج:</h6>
                                <ul>
                                    <li>تأكد من أن محتوى الخلايا واضح ومفهوم</li>
                                    <li>استخدم التعليمات الإضافية لتوجيه الذكاء الصناعي</li>
                                    <li>اختر نمط التصميم المناسب لنوع المحتوى</li>
                                    <li>راجع الإعدادات قبل بدء المعالجة</li>
                                </ul>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        const modalsContainer = document.getElementById('modalsContainer') || document.body;
        modalsContainer.insertAdjacentHTML('beforeend', helpHTML);
    }
    
    /**
     * Show welcome message
     */
    showWelcomeMessage() {
        this.showInfoNotification('مرحباً بك في أداة تحويل Excel إلى HTML بالذكاء الصناعي!');
    }
    
    /**
     * Notification methods
     */
    showSuccessNotification(message) {
        this.showNotification(message, 'success');
    }
    
    showErrorNotification(message) {
        this.showNotification(message, 'error');
    }
    
    showInfoNotification(message) {
        this.showNotification(message, 'info');
    }
    
    showLoadingNotification(message) {
        this.showNotification(message, 'loading');
    }
    
    showNotification(message, type = 'info') {
        // Simple notification implementation
        // In a real app, you might use a more sophisticated notification library
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // You can implement toast notifications here
        // For now, we'll use browser alerts for critical messages
        if (type === 'error') {
            alert(message);
        }
    }
    
    /**
     * Get application state
     */
    getState() {
        return {
            isInitialized: this.isInitialized,
            hasData: !!this.currentData,
            dataCount: this.currentData ? this.currentData.length : 0,
            hasResults: !!this.processingResults,
            isProcessing: window.aiProcessor ? window.aiProcessor.isProcessing : false
        };
    }
}

// Initialize the application
window.app = new ExcelToHTMLApp();

// Export for debugging
window.ExcelToHTMLApp = ExcelToHTMLApp;
