/**
 * Progress Manager for tracking and displaying processing progress
 * Handles real-time updates, statistics, and user controls
 */

class ProgressManager {
    constructor() {
        this.isActive = false;
        this.currentProgress = 0;
        this.startTime = null;
        this.lastUpdateTime = null;
        this.processedCount = 0;
        this.totalCount = 0;
        this.processingSpeed = 0;
        this.estimatedTimeRemaining = 0;
        this.updateInterval = null;
        this.animationFrame = null;
        
        // Initialize UI elements
        this.initializeElements();
        
        // Bind event listeners
        this.bindEvents();
    }
    
    /**
     * Initialize UI elements
     */
    initializeElements() {
        this.elements = {
            progressSection: document.getElementById('progressSection'),
            progressBar: document.getElementById('progressBar'),
            progressPercent: document.getElementById('progressPercent'),
            progressTitle: document.getElementById('progressTitle'),
            progressDescription: document.getElementById('progressDescription'),
            processedCount: document.getElementById('processedCount'),
            remainingCount: document.getElementById('remainingCount'),
            estimatedTime: document.getElementById('estimatedTime'),
            processingSpeed: document.getElementById('processingSpeed'),
            pauseBtn: document.getElementById('pauseBtn'),
            stopBtn: document.getElementById('stopBtn')
        };
    }
    
    /**
     * Bind event listeners
     */
    bindEvents() {
        // Pause button
        if (this.elements.pauseBtn) {
            this.elements.pauseBtn.addEventListener('click', () => {
                this.togglePause();
            });
        }
        
        // Stop button
        if (this.elements.stopBtn) {
            this.elements.stopBtn.addEventListener('click', () => {
                this.stopProcessing();
            });
        }
        
        // Listen for AI processor events
        window.addEventListener('processingStarted', (e) => {
            this.startProgress(e.detail);
        });
        
        window.addEventListener('processingPaused', () => {
            this.pauseProgress();
        });
        
        window.addEventListener('processingResumed', () => {
            this.resumeProgress();
        });
        
        window.addEventListener('processingStopped', () => {
            this.stopProgress();
        });
        
        window.addEventListener('processingCompleted', (e) => {
            this.completeProgress(e.detail);
        });
    }
    
    /**
     * Start progress tracking
     */
    startProgress(options = {}) {
        this.isActive = true;
        this.startTime = Date.now();
        this.lastUpdateTime = this.startTime;
        this.currentProgress = 0;
        this.processedCount = 0;
        this.totalCount = options.totalCount || 0;
        this.processingSpeed = 0;
        this.estimatedTimeRemaining = 0;
        
        // Show progress section
        this.showProgressSection();
        
        // Start update interval
        this.startUpdateInterval();
        
        // Initial update
        this.updateProgress(0, 'بدء المعالجة...', 'جاري تحضير البيانات للمعالجة');
        
        // Add animation classes
        this.addAnimations();
    }
    
    /**
     * Update progress
     */
    updateProgress(percentage, title, description, stats = {}) {
        if (!this.isActive) return;
        
        // Update progress data
        if (percentage !== null && percentage !== undefined) {
            this.currentProgress = Math.max(0, Math.min(100, percentage));
        }
        
        if (stats.processedCount !== undefined) {
            this.processedCount = stats.processedCount;
        }
        
        if (stats.totalCount !== undefined) {
            this.totalCount = stats.totalCount;
        }
        
        // Calculate statistics
        this.calculateStatistics();
        
        // Update UI elements
        this.updateProgressBar();
        this.updateProgressText(title, description);
        this.updateStatistics();
        
        // Update last update time
        this.lastUpdateTime = Date.now();
    }
    
    /**
     * Calculate processing statistics
     */
    calculateStatistics() {
        const now = Date.now();
        const elapsedTime = now - this.startTime;
        
        if (elapsedTime > 0 && this.processedCount > 0) {
            // Calculate processing speed (items per minute)
            this.processingSpeed = (this.processedCount / elapsedTime) * 60000;
            
            // Calculate estimated time remaining
            const remainingItems = this.totalCount - this.processedCount;
            if (this.processingSpeed > 0 && remainingItems > 0) {
                this.estimatedTimeRemaining = (remainingItems / this.processingSpeed) * 60000;
            } else {
                this.estimatedTimeRemaining = 0;
            }
        }
    }
    
    /**
     * Update progress bar
     */
    updateProgressBar() {
        if (!this.elements.progressBar) return;
        
        // Animate progress bar
        this.animateProgressBar(this.currentProgress);
        
        // Update percentage text
        if (this.elements.progressPercent) {
            this.elements.progressPercent.textContent = `${Math.round(this.currentProgress)}%`;
        }
    }
    
    /**
     * Animate progress bar
     */
    animateProgressBar(targetProgress) {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
        
        const progressBar = this.elements.progressBar;
        if (!progressBar) return;
        
        const currentWidth = parseFloat(progressBar.style.width) || 0;
        const targetWidth = targetProgress;
        const difference = targetWidth - currentWidth;
        
        if (Math.abs(difference) < 0.1) {
            progressBar.style.width = `${targetWidth}%`;
            return;
        }
        
        const step = difference * 0.1;
        const newWidth = currentWidth + step;
        
        progressBar.style.width = `${newWidth}%`;
        
        this.animationFrame = requestAnimationFrame(() => {
            this.animateProgressBar(targetProgress);
        });
    }
    
    /**
     * Update progress text
     */
    updateProgressText(title, description) {
        if (title && this.elements.progressTitle) {
            this.elements.progressTitle.textContent = title;
        }
        
        if (description && this.elements.progressDescription) {
            this.elements.progressDescription.textContent = description;
        }
    }
    
    /**
     * Update statistics display
     */
    updateStatistics() {
        // Update processed count
        if (this.elements.processedCount) {
            this.elements.processedCount.textContent = this.processedCount.toLocaleString('ar-SA');
        }
        
        // Update remaining count
        if (this.elements.remainingCount) {
            const remaining = Math.max(0, this.totalCount - this.processedCount);
            this.elements.remainingCount.textContent = remaining.toLocaleString('ar-SA');
        }
        
        // Update estimated time
        if (this.elements.estimatedTime) {
            this.elements.estimatedTime.textContent = this.formatTime(this.estimatedTimeRemaining);
        }
        
        // Update processing speed
        if (this.elements.processingSpeed) {
            const speed = Math.round(this.processingSpeed);
            this.elements.processingSpeed.textContent = `${speed} عنصر/دقيقة`;
        }
    }
    
    /**
     * Format time duration
     */
    formatTime(milliseconds) {
        if (!milliseconds || milliseconds <= 0) {
            return '--:--';
        }
        
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
    }
    
    /**
     * Show progress section
     */
    showProgressSection() {
        if (this.elements.progressSection) {
            this.elements.progressSection.style.display = 'block';
            this.elements.progressSection.classList.add('animate-fadeIn');
        }
    }
    
    /**
     * Hide progress section
     */
    hideProgressSection() {
        if (this.elements.progressSection) {
            this.elements.progressSection.classList.add('animate-fadeOut');
            setTimeout(() => {
                this.elements.progressSection.style.display = 'none';
                this.elements.progressSection.classList.remove('animate-fadeOut');
            }, 300);
        }
    }
    
    /**
     * Add animations to progress elements
     */
    addAnimations() {
        if (this.elements.progressBar) {
            this.elements.progressBar.classList.add('progress-bar-animated');
        }
        
        // Add stagger animation to stat items
        const statItems = document.querySelectorAll('.stat-item');
        statItems.forEach((item, index) => {
            item.classList.add('stagger-item');
            item.style.animationDelay = `${index * 0.1}s`;
        });
    }
    
    /**
     * Toggle pause/resume
     */
    togglePause() {
        if (window.aiProcessor) {
            if (window.aiProcessor.isPaused) {
                window.aiProcessor.resumeProcessing();
                this.resumeProgress();
            } else {
                window.aiProcessor.pauseProcessing();
                this.pauseProgress();
            }
        }
    }
    
    /**
     * Pause progress
     */
    pauseProgress() {
        this.stopUpdateInterval();
        
        // Update pause button
        if (this.elements.pauseBtn) {
            this.elements.pauseBtn.innerHTML = '<i class="fas fa-play"></i> استئناف';
            this.elements.pauseBtn.classList.remove('btn-warning');
            this.elements.pauseBtn.classList.add('btn-success');
        }
        
        // Add paused state to progress bar
        if (this.elements.progressBar) {
            this.elements.progressBar.classList.add('progress-bar-paused');
            this.elements.progressBar.classList.remove('progress-bar-animated');
        }
    }
    
    /**
     * Resume progress
     */
    resumeProgress() {
        this.startUpdateInterval();
        
        // Update pause button
        if (this.elements.pauseBtn) {
            this.elements.pauseBtn.innerHTML = '<i class="fas fa-pause"></i> إيقاف مؤقت';
            this.elements.pauseBtn.classList.remove('btn-success');
            this.elements.pauseBtn.classList.add('btn-warning');
        }
        
        // Remove paused state from progress bar
        if (this.elements.progressBar) {
            this.elements.progressBar.classList.remove('progress-bar-paused');
            this.elements.progressBar.classList.add('progress-bar-animated');
        }
    }
    
    /**
     * Stop processing
     */
    stopProcessing() {
        if (window.aiProcessor) {
            window.aiProcessor.stopProcessing();
        }
        this.stopProgress();
    }
    
    /**
     * Stop progress
     */
    stopProgress() {
        this.isActive = false;
        this.stopUpdateInterval();
        
        // Update UI to stopped state
        this.updateProgress(null, 'تم إيقاف المعالجة', 'تم إيقاف العملية بواسطة المستخدم');
        
        // Hide progress section after delay
        setTimeout(() => {
            this.hideProgressSection();
        }, 3000);
    }
    
    /**
     * Complete progress
     */
    completeProgress(results = {}) {
        this.isActive = false;
        this.stopUpdateInterval();
        
        // Update to 100%
        this.updateProgress(100, 'اكتملت المعالجة بنجاح', 'تم إنهاء جميع العمليات');
        
        // Add completion animation
        if (this.elements.progressBar) {
            this.elements.progressBar.classList.add('progress-bar-complete');
        }
        
        // Show results section
        setTimeout(() => {
            this.showResultsSection(results);
        }, 1000);
    }
    
    /**
     * Show results section
     */
    showResultsSection(results) {
        const resultsSection = document.getElementById('resultsSection');
        if (resultsSection) {
            resultsSection.style.display = 'block';
            resultsSection.classList.add('animate-fadeIn');
            
            // Update results statistics
            this.updateResultsStatistics(results);
        }
    }
    
    /**
     * Update results statistics
     */
    updateResultsStatistics(results) {
        const totalProcessedEl = document.getElementById('totalProcessed');
        const totalTimeEl = document.getElementById('totalTime');
        const successRateEl = document.getElementById('successRate');
        
        if (totalProcessedEl && results.successful !== undefined) {
            totalProcessedEl.textContent = results.successful.toLocaleString('ar-SA');
        }
        
        if (totalTimeEl && results.processingTime !== undefined) {
            totalTimeEl.textContent = this.formatTime(results.processingTime);
        }
        
        if (successRateEl && results.successRate !== undefined) {
            successRateEl.textContent = `${Math.round(results.successRate)}%`;
        }
    }
    
    /**
     * Start update interval
     */
    startUpdateInterval() {
        this.stopUpdateInterval();
        this.updateInterval = setInterval(() => {
            if (this.isActive && window.aiProcessor) {
                const stats = window.aiProcessor.getStatistics();
                this.updateProgress(null, null, null, stats);
            }
        }, CONFIG.UI.PROGRESS_UPDATE_INTERVAL);
    }
    
    /**
     * Stop update interval
     */
    stopUpdateInterval() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }
    }
    
    /**
     * Get current progress data
     */
    getProgressData() {
        return {
            isActive: this.isActive,
            currentProgress: this.currentProgress,
            processedCount: this.processedCount,
            totalCount: this.totalCount,
            processingSpeed: this.processingSpeed,
            estimatedTimeRemaining: this.estimatedTimeRemaining,
            elapsedTime: this.startTime ? Date.now() - this.startTime : 0
        };
    }
    
    /**
     * Reset progress
     */
    reset() {
        this.isActive = false;
        this.currentProgress = 0;
        this.processedCount = 0;
        this.totalCount = 0;
        this.processingSpeed = 0;
        this.estimatedTimeRemaining = 0;
        this.startTime = null;
        this.lastUpdateTime = null;
        
        this.stopUpdateInterval();
        this.hideProgressSection();
        
        // Reset UI elements
        if (this.elements.progressBar) {
            this.elements.progressBar.style.width = '0%';
            this.elements.progressBar.classList.remove('progress-bar-complete', 'progress-bar-paused');
        }
        
        if (this.elements.pauseBtn) {
            this.elements.pauseBtn.innerHTML = '<i class="fas fa-pause"></i> إيقاف مؤقت';
            this.elements.pauseBtn.classList.remove('btn-success');
            this.elements.pauseBtn.classList.add('btn-warning');
        }
    }
}

// Create global instance
window.progressManager = new ProgressManager();
